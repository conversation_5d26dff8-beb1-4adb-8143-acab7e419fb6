using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace PowerVoicr
{
    public partial class KeyTester : Form
    {
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);
        
        private const int VK_RMENU = 0xA5; // Right Alt
        private const int VK_LMENU = 0xA4; // Left Alt
        
        private TextBox outputTextBox;
        private System.Windows.Forms.Timer checkTimer;
        
        public KeyTester()
        {
            InitializeComponent();
            this.KeyPreview = true;
            this.KeyDown += KeyTester_KeyDown;
            this.KeyUp += KeyTester_KeyUp;
            
            // Set up timer to continuously check key states
            checkTimer = new System.Windows.Forms.Timer();
            checkTimer.Interval = 100; // Check every 100ms
            checkTimer.Tick += CheckTimer_Tick;
            checkTimer.Start();
        }
        
        private void InitializeComponent()
        {
            this.outputTextBox = new TextBox();
            this.SuspendLayout();
            
            // outputTextBox
            this.outputTextBox.Dock = DockStyle.Fill;
            this.outputTextBox.Multiline = true;
            this.outputTextBox.ScrollBars = ScrollBars.Vertical;
            this.outputTextBox.ReadOnly = true;
            this.outputTextBox.Font = new System.Drawing.Font("Consolas", 9F);
            
            // KeyTester
            this.Text = "Key Tester - Press Right Alt Key";
            this.Size = new System.Drawing.Size(600, 400);
            this.Controls.Add(this.outputTextBox);
            this.ResumeLayout(false);
        }
        
        private void CheckTimer_Tick(object sender, EventArgs e)
        {
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            
            if (rightAlt || leftAlt)
            {
                string msg = $"[API] {DateTime.Now:HH:mm:ss.fff} - Right Alt: {rightAlt}, Left Alt: {leftAlt}";
                AppendOutput(msg);
            }
        }
        
        private void KeyTester_KeyDown(object sender, KeyEventArgs e)
        {
            string msg = $"[KeyDown] {DateTime.Now:HH:mm:ss.fff} - KeyCode: {e.KeyCode}, Modifiers: {e.Modifiers}";
            
            // Check API state immediately
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            msg += $" | API: Right={rightAlt}, Left={leftAlt}";
            
            AppendOutput(msg);
        }
        
        private void KeyTester_KeyUp(object sender, KeyEventArgs e)
        {
            string msg = $"[KeyUp] {DateTime.Now:HH:mm:ss.fff} - KeyCode: {e.KeyCode}, Modifiers: {e.Modifiers}";
            
            // Check API state immediately
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            msg += $" | API: Right={rightAlt}, Left={leftAlt}";
            
            AppendOutput(msg);
        }
        
        private void AppendOutput(string message)
        {
            if (outputTextBox.InvokeRequired)
            {
                outputTextBox.Invoke(new Action<string>(AppendOutput), message);
                return;
            }
            
            outputTextBox.AppendText(message + Environment.NewLine);
            outputTextBox.SelectionStart = outputTextBox.Text.Length;
            outputTextBox.ScrollToCaret();
        }
        
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                checkTimer?.Stop();
                checkTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
