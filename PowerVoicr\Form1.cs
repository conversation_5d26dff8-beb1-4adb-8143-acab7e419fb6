using System.Text;
using System.Windows.Forms;
using PowerVoicr.Services;
using PowerVoicr.Services.Interfaces;
using System.Drawing.Drawing2D;

namespace PowerVoicr
{
    public partial class Form1 : Form
    {
        private readonly ISpeechRecognitionService speechService;
        private readonly IHotkeyService hotkeyService;
        private readonly ICommandService commandService;
        private readonly IInputSimulationService inputSimulator;
        private NotifyIcon? trayIcon;
        private bool isListening = false;
        private StringBuilder transcribedTextBuilder = new StringBuilder();
        private StringBuilder currentSessionBuffer = new StringBuilder();
        private FloatingRecordingIndicator? floatingIndicator;

        // Variables for moving the form (since we have FormBorderStyle.None)
        private bool isDragging = false;
        private Point dragStartPoint;

        // Sci-fi UI classes
        private class Particle
        {
            public float X { get; set; }
            public float Y { get; set; }
            public float Speed { get; set; }
            public float Size { get; set; }
            public float Alpha { get; set; }
            public float Direction { get; set; }
            public Color Color { get; set; }

            public void Update()
            {
                X += (float)Math.Cos(Direction) * Speed;
                Y += (float)Math.Sin(Direction) * Speed;
                Alpha -= 0.01f;
            }

            public void Draw(Graphics g)
            {
                if (Alpha <= 0) return;

                using (SolidBrush brush = new SolidBrush(Color.FromArgb((int)(Alpha * 255), Color)))
                {
                    g.FillEllipse(brush, X, Y, Size, Size);
                }
            }
        }

        public Form1()
        {
            InitializeComponent();

            try
            {
                // Get services from ServiceProvider
                speechService = ServiceProvider.Instance.GetService<ISpeechRecognitionService>();
                hotkeyService = ServiceProvider.Instance.GetService<IHotkeyService>();
                commandService = ServiceProvider.Instance.GetService<ICommandService>();
                inputSimulator = ServiceProvider.Instance.GetService<IInputSimulationService>();

                // Initialize services
                InitializeServices();
                InitializeTrayIcon();
                InitializeFloatingIndicator();

                // Subscribe to events
                SubscribeToEvents();

                // Show instructions on startup
                ShowInstructions();

                // Start animation timer
                animationTimer.Start();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during form initialization: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Animation and UI related methods
        private void Form1_Load(object sender, EventArgs e)
        {
            // Add initial particles
            for (int i = 0; i < 20; i++)
            {
                AddParticle(random.Next(Width), random.Next(Height));
            }

            // Set form to have rounded corners
            Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, Width, Height, 15, 15));
        }

        private void Form1_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // Draw glowing border
            float borderGlow = 0.5f + (float)Math.Sin(pulseValue) * 0.5f;
            using (Pen borderPen = new Pen(Color.FromArgb((int)(200 * borderGlow), 0, 200, 255), 2))
            {
                g.DrawRectangle(borderPen, 1, 1, Width - 3, Height - 3);
            }

            // Create science fiction style grid lines
            using (Pen gridPen = new Pen(Color.FromArgb(30, 0, 200, 255), 1))
            {
                // Horizontal grid lines
                for (int y = 0; y < Height; y += 20)
                {
                    g.DrawLine(gridPen, 0, y, Width, y);
                }

                // Vertical grid lines
                for (int x = 0; x < Width; x += 20)
                {
                    g.DrawLine(gridPen, x, 0, x, Height);
                }
            }

            // Draw decorative lights
            using (Brush glow = new SolidBrush(Color.FromArgb((int)(50 * (0.5f + (float)Math.Sin(pulseValue) * 0.5f)), 0, 200, 255)))
            {
                g.FillEllipse(glow, 10, 10, 5, 5);
                g.FillEllipse(glow, Width - 15, 10, 5, 5);
                g.FillEllipse(glow, Width - 15, Height - 15, 5, 5);
                g.FillEllipse(glow, 10, Height - 15, 5, 5);
            }

            // Draw a close button in the top right
            Rectangle closeRect = new Rectangle(Width - 30, 5, 20, 20);
            using (SolidBrush closeBrush = new SolidBrush(Color.FromArgb(180, 0, 200, 255)))
            {
                g.FillRectangle(closeBrush, closeRect);
                using (Pen closePen = new Pen(Color.White, 2))
                {
                    g.DrawLine(closePen, closeRect.X + 5, closeRect.Y + 5, closeRect.X + closeRect.Width - 5, closeRect.Y + closeRect.Height - 5);
                    g.DrawLine(closePen, closeRect.X + closeRect.Width - 5, closeRect.Y + 5, closeRect.X + 5, closeRect.Y + closeRect.Height - 5);
                }
            }

            // Draw particles
            foreach (var particle in particles.ToList())
            {
                particle.Draw(g);
            }
        }

        private void animationTimer_Tick(object sender, EventArgs e)
        {
            pulseValue += 0.05f;

            // Add random particles
            if (random.Next(100) < 5)
            {
                AddParticle(random.Next(Width), random.Next(Height));
            }

            // Update particles
            for (int i = particles.Count - 1; i >= 0; i--)
            {
                particles[i].Update();
                if (particles[i].Alpha <= 0)
                {
                    particles.RemoveAt(i);
                }
            }

            // Create a special effect when listening
            if (isListening && random.Next(100) < 20)
            {
                // Add particles around the listening button
                int x = btnToggleListening.Left + random.Next(btnToggleListening.Width);
                int y = btnToggleListening.Top + random.Next(btnToggleListening.Height);
                AddParticle(x, y, Color.FromArgb(0, 255, 0));
            }

            Invalidate(); // Force redraw
        }

        private void AddParticle(float x, float y, Color? color = null)
        {
            Color particleColor = color ?? Color.FromArgb(0, 150 + random.Next(105), 255);
            particles.Add(new Particle
            {
                X = x,
                Y = y,
                Speed = 0.1f + (float)random.NextDouble() * 0.5f,
                Size = 1 + (float)random.NextDouble() * 3,
                Alpha = 0.2f + (float)random.NextDouble() * 0.8f,
                Direction = (float)(random.NextDouble() * Math.PI * 2),
                Color = particleColor
            });
        }

        private void Form1_MouseDown(object sender, MouseEventArgs e)
        {
            // Check if we clicked the close button
            if (e.X >= Width - 30 && e.X <= Width - 10 && e.Y >= 5 && e.Y <= 25)
            {
                Exit(this, EventArgs.Empty);
                return;
            }

            // Allow dragging the form
            isDragging = true;
            dragStartPoint = new Point(e.X, e.Y);

            // Add particles where clicked
            for (int i = 0; i < 5; i++)
            {
                AddParticle(e.X, e.Y);
            }
        }

        private void Form1_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                Point currentPoint = PointToScreen(new Point(e.X, e.Y));
                currentPoint.Offset(-dragStartPoint.X, -dragStartPoint.Y);
                Location = currentPoint;
            }
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            isDragging = false;
            base.OnMouseUp(e);
        }

        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(int nLeftRect, int nTopRect, int nRightRect, int nBottomRect, int nWidthEllipse, int nHeightEllipse);

        private void InitializeServices()
        {
            try
            {
                hotkeyService.Initialize();
                LoadHotkeyFromSettings();

                // Initialize speech services
                InitializeSpeechService();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing services: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void InitializeSpeechService()
        {
            try
            {
                await speechService.InitializeAsync();
                UpdateStatusText(speechService.IsConfigured ?
                    "Azure Speech Service configured successfully." :
                    "Azure Speech Service not configured. Please configure for better quality.");
            }
            catch (Exception ex)
            {
                UpdateStatusText($"Speech service initialization failed: {ex.Message}");
            }
        }

        private void SubscribeToEvents()
        {
            // Subscribe to form events
            this.FormClosing += (s, e) => Form1_FormClosing(s!, e);
            this.Resize += (s, e) => Form1_Resize(s!, e);

            // Subscribe to speech service events
            speechService.TextRecognized += (s, word) => SpeechService_TextRecognized(s!, word);
            speechService.RecognitionInProgress += (s, word) => SpeechService_Recognizing(s!, word);
            speechService.RecognitionCanceled += (s, reason) => SpeechService_Canceled(s!, reason);

            // Subscribe to hotkey service events
            hotkeyService.HotkeyPressed += (s, e) => HotkeyService_HotkeyPressed(s!, e);
            hotkeyService.HotkeyReleased += (s, e) => HotkeyService_HotkeyReleased(s!, e);
        }

        private void InitializeTrayIcon()
        {
            trayIcon = new NotifyIcon
            {
                Icon = SystemIcons.Application,
                Text = "PowerVoicr - Voice Transcription",
                Visible = true
            };

            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.Add("Settings", null, (s, e) => ShowMainWindow());
            contextMenu.Items.Add("Azure Configuration", null, (s, e) => ShowAzureConfig(s!, e));
            contextMenu.Items.Add("Instructions", null, (s, e) => ShowInstructions());
            contextMenu.Items.Add("-");
            contextMenu.Items.Add("Exit", null, (s, e) => Exit(s!, e));

            trayIcon.ContextMenuStrip = contextMenu;
            trayIcon.DoubleClick += (s, e) => ShowMainWindow();
        }

        private void InitializeFloatingIndicator()
        {
            floatingIndicator = new FloatingRecordingIndicator();
        }

        private void LoadHotkeyFromSettings()
        {
            try
            {
                string modifiers = Settings.Instance.HotkeyModifiers;
                string key = Settings.Instance.HotkeyKey;

                if (!string.IsNullOrEmpty(key))
                {
                    if (Enum.TryParse<Keys>(key, out Keys hotkeyKey))
                    {
                        Keys modifierKeys = Keys.None;
                        if (!string.IsNullOrEmpty(modifiers))
                        {
                            foreach (string mod in modifiers.Split(','))
                            {
                                if (Enum.TryParse(mod.Trim(), out Keys modKey))
                                {
                                    modifierKeys |= modKey;
                                }
                            }
                        }

                        hotkeyService.SetHotkey(hotkeyKey, modifierKeys);
                    }
                }

                // Update the current hotkey label
                lblCurrentHotkey.Text = $"Current Hotkey: {hotkeyService.GetHotkeyDisplayText()}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading hotkey settings: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void HotkeyService_HotkeyPressed(object sender, EventArgs e)
        {
            StartListening();
        }

        private void HotkeyService_HotkeyReleased(object sender, EventArgs e)
        {
            StopListening();
        }

        private void SpeechService_TextRecognized(object sender, string text)
        {
            if (isListening && !string.IsNullOrEmpty(text))
            {
                try
                {
                    transcribedTextBuilder.Append(text).Append(" ");
                    UpdateTranscriptionDisplay(transcribedTextBuilder.ToString());

                    // Update floating indicator with final transcription
                    floatingIndicator?.UpdateFinalTranscription(text);

                    // Process text immediately
                    bool isCommand = commandService.ProcessText(text);
                    if (!isCommand)
                    {
                        inputSimulator.TypeText(text);
                        inputSimulator.PressSpace();
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatusText($"Error processing text: {ex.Message}");
                }
            }
        }

        private void SpeechService_Recognizing(object sender, string word)
        {
            if (isListening)
            {
                UpdateStatusText($"Recognizing: {word}");
                UpdateTranscriptionDisplay($"{transcribedTextBuilder}[{word}]");

                // Update floating indicator with real-time transcription
                floatingIndicator?.UpdateTranscription(word);
            }
        }

        private void SpeechService_Canceled(object sender, string reason)
        {
            if (isListening)
            {
                UpdateStatusText($"Recognition canceled: {reason}");
            }
        }

        private void ProcessWord(string word)
        {
            if (string.IsNullOrEmpty(word)) return;

            try
            {
                bool isCommand = commandService.ProcessText(word);

                if (!isCommand)
                {
                    // Type the word into the active window
                    inputSimulator.TypeText(word);
                    inputSimulator.PressSpace();
                }
            }
            catch (Exception ex)
            {
                UpdateStatusText($"Error processing word: {ex.Message}");
            }
        }

        private async void StartListening()
        {
            if (!isListening)
            {
                isListening = true;

                try
                {
                    // Clear both the display text and current session buffer
                    transcribedTextBuilder.Clear();
                    currentSessionBuffer.Clear();
                    if (txtTranscription != null)
                    {
                        txtTranscription.Clear();
                    }

                    // Show floating recording indicator
                    floatingIndicator?.StartRecording();

                    await speechService.StartListeningAsync();
                    UpdateStatusText("Speech recognition started. Speak now...");
                    trayIcon!.Icon = SystemIcons.Information;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to start speech recognition: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    isListening = false;
                    floatingIndicator?.StopRecording();
                }

                UpdateListeningStatus();
            }
        }

        private async void StopListening()
        {
            if (isListening)
            {
                try
                {
                    await speechService.StopListeningAsync();
                    UpdateStatusText("Speech recognition stopped.");
                    trayIcon!.Icon = SystemIcons.Application;

                    // Hide floating recording indicator
                    floatingIndicator?.StopRecording();

                    // do this in another task
                    isListening = false;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to stop speech recognition: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    floatingIndicator?.StopRecording();
                }
                UpdateListeningStatus();
            }
        }

        private void ShowMainWindow()
        {
            this.Show();
            this.WindowState = FormWindowState.Normal;
            this.Activate();
        }

        private void ShowAzureConfig(object sender, EventArgs e)
        {
            var configForm = new ConfigForm();
            if (configForm.ShowDialog() == DialogResult.OK)
            {
                InitializeSpeechService();
            }
        }

        private void btnAzureConfig_Click(object sender, EventArgs e)
        {
            ShowAzureConfig(sender, e);
        }

        private void UpdateStatusText(string message)
        {
            if (lblStatus.InvokeRequired)
            {
                lblStatus.Invoke(new Action(() => lblStatus.Text = message));
            }
            else
            {
                lblStatus.Text = message;
            }
        }

        private void UpdateTranscriptionDisplay(string fullTranscription)
        {
            if (txtTranscription != null)
            {
                if (txtTranscription.InvokeRequired)
                {
                    txtTranscription.Invoke(new Action(() =>
                    {
                        txtTranscription.Text = fullTranscription;
                        txtTranscription.SelectionStart = txtTranscription.Text.Length;
                        txtTranscription.ScrollToCaret();
                    }));
                }
                else
                {
                    txtTranscription.Text = fullTranscription;
                    txtTranscription.SelectionStart = txtTranscription.Text.Length;
                    txtTranscription.ScrollToCaret();
                }
            }
        }

        private void UpdateListeningStatus()
        {
            if (btnToggleListening.InvokeRequired)
            {
                btnToggleListening.Invoke(new Action(() =>
                {
                    btnToggleListening.Text = isListening ? "NEURAL CAPTURE ACTIVE" : "INITIATE VOICE CAPTURE";
                    btnToggleListening.BackColor = isListening ? Color.FromArgb(40, 80, 100) : Color.FromArgb(20, 30, 40);
                    btnToggleListening.ForeColor = isListening ? Color.FromArgb(0, 255, 180) : Color.FromArgb(0, 255, 240);
                }));
            }
            else
            {
                btnToggleListening.Text = isListening ? "NEURAL CAPTURE ACTIVE" : "INITIATE VOICE CAPTURE";
                btnToggleListening.BackColor = isListening ? Color.FromArgb(40, 80, 100) : Color.FromArgb(20, 30, 40);
                btnToggleListening.ForeColor = isListening ? Color.FromArgb(0, 255, 180) : Color.FromArgb(0, 255, 240);
            }
        }

        private void btnToggleListening_Click(object sender, EventArgs e)
        {
            if (isListening)
            {
                StopListening();
            }
            else
            {
                StartListening();
            }
        }

        private void btnChangeHotkey_Click(object sender, EventArgs e)
        {
            if (!hotkeyService.IsRecordingHotkey)
            {
                hotkeyService.IsRecordingHotkey = true;
                btnChangeHotkey.Text = "Press New Hotkey...";
                btnChangeHotkey.BackColor = Color.LightCoral;
                UpdateStatusText("Press the key combination you want to use...");
                btnChangeHotkey.Focus();
            }
            else
            {
                hotkeyService.IsRecordingHotkey = false;
                btnChangeHotkey.Text = "Change Hotkey";
                btnChangeHotkey.BackColor = SystemColors.Control;
                LoadHotkeyFromSettings();
            }
        }

        private void btnClearTranscription_Click(object sender, EventArgs e)
        {
            transcribedTextBuilder.Clear();
            txtTranscription.Clear();
            UpdateStatusText("Transcription cleared.");
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                this.Hide();
            }
            else
            {
                CleanupResources();
            }
        }

        private void Form1_Resize(object sender, EventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Hide();
            }
        }

        private void ShowInstructions()
        {
            string instructions =
                "POWER VOICR X9000 - NEURAL INTERFACE\n\n" +
                "OPERATING INSTRUCTIONS:\n" +
                $"1. PRESS AND HOLD {hotkeyService.GetHotkeyDisplayText()} TO ACTIVATE NEURAL LINK\n" +
                "2. SPEAK CLEARLY TO TRANSMIT VOICE PATTERNS\n" +
                "3. YOUR NEURAL PATTERNS WILL BE TRANSCRIBED TO THE ACTIVE INPUT FIELD\n" +
                $"4. RELEASE {hotkeyService.GetHotkeyDisplayText()} TO TERMINATE NEURAL CONNECTION\n\n" +
                "COMMAND PROTOCOLS:\n" +
                "• EXECUTE COMMANDS BY SPEAKING KEYWORDS (\"ENTER\", \"BACKSPACE\")\n" +
                "• OR USE \"VOICR/VOICER\" PREFIX: \"VOICER ENTER\"\n\n" +
                "FOR OPTIMAL NEURAL RECOGNITION, CONFIGURE AZURE AI MATRIX.\n\n" +
                "NEURAL INTERFACE SETTINGS AVAILABLE IN CONTROL PANEL.\n\n" +
                "MINIMIZE INTERFACE TO OPERATE IN STEALTH MODE.";

            MessageBox.Show(instructions, "POWER VOICR X9000 INTERFACE",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void Exit(object sender, EventArgs e)
        {
            CleanupResources();
            Application.Exit();
        }

        private void CleanupResources()
        {
            // Unsubscribe from events
            speechService.TextRecognized -= (s, word) => SpeechService_TextRecognized(s!, word);
            speechService.RecognitionInProgress -= (s, word) => SpeechService_Recognizing(s!, word);
            speechService.RecognitionCanceled -= (s, reason) => SpeechService_Canceled(s!, reason);

            hotkeyService.HotkeyPressed -= (s, e) => HotkeyService_HotkeyPressed(s!, e);
            hotkeyService.HotkeyReleased -= (s, e) => HotkeyService_HotkeyReleased(s!, e);

            // Dispose services
            (speechService as IDisposable)?.Dispose();
            (hotkeyService as IDisposable)?.Dispose();

            // Dispose floating indicator
            floatingIndicator?.Dispose();

            if (trayIcon != null)
            {
                trayIcon.Visible = false;
                trayIcon.Dispose();
            }
        }
    }
}
