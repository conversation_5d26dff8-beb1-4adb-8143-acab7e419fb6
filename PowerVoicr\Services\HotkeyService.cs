using PowerVoicr.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace PowerVoicr.Services
{
    public class HotkeyService : IHotkeyService
    {
        // Win32 API methods for direct right Alt key detection
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);
        
        private const int VK_RMENU = 0xA5; // Virtual key code for right Alt (Right Menu key)
        private const int VK_LMENU = 0xA4; // Virtual key code for left Alt (Left Menu key)

        private KeyboardHook keyboardHook;
        private bool hotkeyPressed = false;
        private Keys recordedModifiers = Keys.None;
        private Keys recordedKey = Keys.None;

        public event EventHandler HotkeyPressed;
        public event EventHandler HotkeyReleased;

        public Keys HotkeyKey { get; set; } = Keys.RMenu; // Default to Right Alt key
        public Keys HotkeyModifier { get; set; } = Keys.None;
        public bool IsRecordingHotkey { get; set; } = false;

        public HotkeyService()
        {
            keyboardHook = new KeyboardHook();
        }

        public void Initialize()
        {
            // Load hotkey from settings
            LoadHotkeyFromSettings();

            // Hook keyboard events
            keyboardHook.KeyDown += KeyboardHook_KeyDown;
            keyboardHook.KeyUp += KeyboardHook_KeyUp;
            keyboardHook.Install();
        }

        private void LoadHotkeyFromSettings()
        {
            try
            {
                if (!string.IsNullOrEmpty(Settings.Instance.HotkeyKey))
                {
                    HotkeyKey = (Keys)Enum.Parse(typeof(Keys), Settings.Instance.HotkeyKey);
                }

                if (!string.IsNullOrEmpty(Settings.Instance.HotkeyModifiers))
                {
                    HotkeyModifier = Keys.None;
                    string[] modifiers = Settings.Instance.HotkeyModifiers.Split(',');
                    foreach (string mod in modifiers)
                    {
                        var trimmedMod = mod.Trim();
                        if (Enum.TryParse(trimmedMod, out Keys key))
                        {
                            HotkeyModifier |= key;
                        }
                    }
                }
            }
            catch (Exception)
            {
                // If settings loading fails, use defaults
                HotkeyKey = Keys.RMenu;
                HotkeyModifier = Keys.None;
            }
        }

        private void KeyboardHook_KeyDown(object sender, KeyEventArgs e)
        {
            // Add debug logging for troubleshooting
            string debugMsg = $"KeyDown: {e.KeyCode}, Modifiers: {e.Modifiers}, Control.ModifierKeys: {Control.ModifierKeys}";
            System.Diagnostics.Debug.WriteLine(debugMsg);
            Console.WriteLine($"[DEBUG] {debugMsg}");
            
            // UNIVERSAL Right Alt detection - works regardless of settings
            // This will detect right Alt no matter how it's configured
            bool shouldActivateRightAlt = false;
            string detectionMethod = "";
            
            // Method 1: Standard Right Alt detection
            if (e.KeyCode == Keys.RMenu || e.KeyCode == Keys.Menu)
            {
                bool rightAltPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                bool leftAltPressed = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
                bool ctrlPressed = (GetAsyncKeyState(0x11) & 0x8000) != 0; // VK_CONTROL
                bool shiftPressed = (GetAsyncKeyState(0x10) & 0x8000) != 0; // VK_SHIFT
                
                if (rightAltPressed && !leftAltPressed && !ctrlPressed && !shiftPressed)
                {
                    shouldActivateRightAlt = true;
                    detectionMethod = "Standard RMenu/Menu";
                }
            }
            
            // Method 2: Right Alt reported as generic ControlKey (YOUR SYSTEM)
            if (!shouldActivateRightAlt && e.KeyCode == Keys.ControlKey)
            {
                bool rightAltPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                bool leftCtrlPressed = (GetAsyncKeyState(0xA2) & 0x8000) != 0; // VK_LCONTROL
                bool rightCtrlPressed = (GetAsyncKeyState(0xA3) & 0x8000) != 0; // VK_RCONTROL
                bool shiftPressed = (GetAsyncKeyState(0x10) & 0x8000) != 0; // VK_SHIFT
                bool leftAltPressed = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
                
                Console.WriteLine($"[DEBUG] ControlKey analysis - RightAlt: {rightAltPressed}, LeftAlt: {leftAltPressed}, LeftCtrl: {leftCtrlPressed}, RightCtrl: {rightCtrlPressed}, Shift: {shiftPressed}");
                
                // If right Alt is pressed when we get generic ControlKey event, this is the right Alt
                if (rightAltPressed && !leftAltPressed && !shiftPressed && !leftCtrlPressed && !rightCtrlPressed)
                {
                    shouldActivateRightAlt = true;
                    detectionMethod = "RightAlt-as-ControlKey";
                }
            }
            
            // Method 3: Right Alt reported as LControlKey (some other layouts)
            if (!shouldActivateRightAlt && e.KeyCode == Keys.LControlKey)
            {
                bool rightAltPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                bool leftCtrlPressed = (GetAsyncKeyState(0xA2) & 0x8000) != 0; // VK_LCONTROL
                bool rightCtrlPressed = (GetAsyncKeyState(0xA3) & 0x8000) != 0; // VK_RCONTROL
                bool shiftPressed = (GetAsyncKeyState(0x10) & 0x8000) != 0; // VK_SHIFT
                bool leftAltPressed = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
                
                Console.WriteLine($"[DEBUG] LControlKey analysis - RightAlt: {rightAltPressed}, LeftAlt: {leftAltPressed}, LeftCtrl: {leftCtrlPressed}, RightCtrl: {rightCtrlPressed}, Shift: {shiftPressed}");
                
                // If right Alt is pressed when we get LControlKey event, this is the right Alt disguised
                if (rightAltPressed && !leftAltPressed && !shiftPressed)
                {
                    shouldActivateRightAlt = true;
                    detectionMethod = "RightAlt-as-LControlKey";
                }
            }
            
            // ACTIVATE RIGHT ALT if detected
            if (shouldActivateRightAlt && !hotkeyPressed)
            {
                hotkeyPressed = true;
                string activateMsg = $"✅ Right Alt detected via {detectionMethod} - activating hotkey!";
                System.Diagnostics.Debug.WriteLine(activateMsg);
                Console.WriteLine($"[SUCCESS] {activateMsg}");
                HotkeyPressed?.Invoke(this, EventArgs.Empty);
                e.Handled = true;
                return; // Exit early to prevent other logic
            }
            
            // Original hotkey detection logic (for configured hotkeys)
            if (HotkeyKey == Keys.RMenu && HotkeyModifier == Keys.None)
            {
                // TEMPORARY FALLBACK: Also allow Left Alt for testing if Right Alt has issues
                if (e.KeyCode == Keys.LMenu)
                {
                    bool leftAltOnly = !e.Control && !e.Shift &&
                                      (Control.ModifierKeys & Keys.Control) == Keys.None &&
                                      (Control.ModifierKeys & Keys.Shift) == Keys.None;
                    
                    if (leftAltOnly && !hotkeyPressed)
                    {
                        hotkeyPressed = true;
                        string fallbackMsg = "⚠️  FALLBACK: Left Alt detected - activating hotkey! (Please test Right Alt)";
                        System.Diagnostics.Debug.WriteLine(fallbackMsg);
                        Console.WriteLine($"[FALLBACK] {fallbackMsg}");
                        HotkeyPressed?.Invoke(this, EventArgs.Empty);
                        e.Handled = true;
                    }
                }
            }
            else
            {
                // Check if our hotkey combination is pressed (for other hotkey types)
                if (IsHotkeyPressed(e))
                {
                    if (!hotkeyPressed)
                    {
                        hotkeyPressed = true;
                        System.Diagnostics.Debug.WriteLine("Hotkey detected - activating!");
                        Console.WriteLine("[SUCCESS] Hotkey detected - activating!");
                        HotkeyPressed?.Invoke(this, EventArgs.Empty);
                    }
                    e.Handled = true;
                }
            }
        }

        private void KeyboardHook_KeyUp(object sender, KeyEventArgs e)
        {
            // Add debug logging for troubleshooting
            string debugMsg = $"KeyUp: {e.KeyCode}, Modifiers: {e.Modifiers}";
            System.Diagnostics.Debug.WriteLine(debugMsg);
            Console.WriteLine($"[DEBUG] {debugMsg}");
            
            // UNIVERSAL Right Alt release detection - works regardless of settings
            if (hotkeyPressed)
            {
                bool shouldDeactivateRightAlt = false;
                string detectionMethod = "";
                
                // Method 1: Standard Right Alt release
                if (e.KeyCode == Keys.RMenu || e.KeyCode == Keys.Menu)
                {
                    bool rightAltStillPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                    if (!rightAltStillPressed)
                    {
                        shouldDeactivateRightAlt = true;
                        detectionMethod = "Standard RMenu/Menu";
                    }
                }
                
                // Method 2: Right Alt reported as generic ControlKey release (YOUR SYSTEM)
                if (!shouldDeactivateRightAlt && e.KeyCode == Keys.ControlKey)
                {
                    bool rightAltStillPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                    if (!rightAltStillPressed)
                    {
                        shouldDeactivateRightAlt = true;
                        detectionMethod = "RightAlt-as-ControlKey";
                    }
                }
                
                // Method 3: Right Alt reported as LControlKey release
                if (!shouldDeactivateRightAlt && e.KeyCode == Keys.LControlKey)
                {
                    bool rightAltStillPressed = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
                    if (!rightAltStillPressed)
                    {
                        shouldDeactivateRightAlt = true;
                        detectionMethod = "RightAlt-as-LControlKey";
                    }
                }
                
                // DEACTIVATE if right Alt is released
                if (shouldDeactivateRightAlt)
                {
                    hotkeyPressed = false;
                    string deactivateMsg = $"✅ Right Alt released via {detectionMethod} - deactivating hotkey!";
                    System.Diagnostics.Debug.WriteLine(deactivateMsg);
                    Console.WriteLine($"[SUCCESS] {deactivateMsg}");
                    HotkeyReleased?.Invoke(this, EventArgs.Empty);
                    e.Handled = true;
                    return; // Exit early
                }
                
                // TEMPORARY FALLBACK: Also handle Left Alt release
                if (e.KeyCode == Keys.LMenu)
                {
                    hotkeyPressed = false;
                    string fallbackMsg = "⚠️  FALLBACK: Left Alt released - deactivating hotkey!";
                    System.Diagnostics.Debug.WriteLine(fallbackMsg);
                    Console.WriteLine($"[FALLBACK] {fallbackMsg}");
                    HotkeyReleased?.Invoke(this, EventArgs.Empty);
                    e.Handled = true;
                    return;
                }
                
                // Check if any part of our hotkey combination is released (for other hotkey types)
                if (IsPartOfHotkey(e.KeyCode))
                {
                    hotkeyPressed = false;
                    System.Diagnostics.Debug.WriteLine("Hotkey released - deactivating!");
                    Console.WriteLine("[SUCCESS] Hotkey released - deactivating!");
                    HotkeyReleased?.Invoke(this, EventArgs.Empty);
                    e.Handled = true;
                }
            }
        }

        private bool IsHotkeyPressed(KeyEventArgs e)
        {
            // For Right Alt-only activation, we need to ensure ONLY Right Alt is pressed
            if (HotkeyKey == Keys.RMenu && HotkeyModifier == Keys.None)
            {
                // Check if this is specifically the right Alt key being pressed
                bool isRightAlt = (e.KeyCode == Keys.RMenu);

                // Additional check using Win32 API for more reliable right Alt detection
                if (!isRightAlt && e.KeyCode == Keys.Menu)
                {
                    // Sometimes right Alt is reported as generic Menu key
                    // Use Win32 API to verify it's specifically the right Alt
                    short rightAltState = GetAsyncKeyState(VK_RMENU);
                    short leftAltState = GetAsyncKeyState(0xA4); // VK_LMENU
                    
                    isRightAlt = (rightAltState & 0x8000) != 0 && (leftAltState & 0x8000) == 0;
                    System.Diagnostics.Debug.WriteLine($"Generic Menu key detected. Right Alt API check: {isRightAlt}");
                }

                if (isRightAlt)
                {
                    // Ensure no other modifier keys are pressed
                    // Check both event flags and actual key state
                    bool noOtherModifiers = !e.Control && !e.Shift &&
                                          (Control.ModifierKeys & Keys.Control) == Keys.None &&
                                          (Control.ModifierKeys & Keys.Shift) == Keys.None;

                    // Additional Win32 API check for modifier keys
                    short ctrlState = GetAsyncKeyState(0x11); // VK_CONTROL
                    short shiftState = GetAsyncKeyState(0x10); // VK_SHIFT
                    short leftAltState = GetAsyncKeyState(0xA4); // VK_LMENU
                    
                    bool noModifiersAPI = (ctrlState & 0x8000) == 0 && 
                                         (shiftState & 0x8000) == 0 && 
                                         (leftAltState & 0x8000) == 0;

                    bool finalResult = noOtherModifiers && noModifiersAPI;
                    System.Diagnostics.Debug.WriteLine($"Right Alt check - Event modifiers OK: {noOtherModifiers}, API modifiers OK: {noModifiersAPI}, Final: {finalResult}");
                    
                    return finalResult;
                }

                // Do NOT respond to left Alt key - only right Alt key should activate
                System.Diagnostics.Debug.WriteLine($"Key {e.KeyCode} is not right Alt, ignoring");
                return false;
            }

            // For other hotkey combinations, use the original logic
            bool controlMatches = ((HotkeyModifier & Keys.Control) != Keys.Control) || e.Control;
            bool altMatches = ((HotkeyModifier & Keys.Alt) != Keys.Alt) || e.Alt;
            bool shiftMatches = ((HotkeyModifier & Keys.Shift) != Keys.Shift) || e.Shift;
            bool keyMatches = e.KeyCode == HotkeyKey;

            return controlMatches && altMatches && shiftMatches && keyMatches;
        }

        private bool IsPartOfHotkey(Keys keyCode)
        {
            // For Right Alt-only activation, only the right Alt key is part of the hotkey
            if (HotkeyKey == Keys.RMenu && HotkeyModifier == Keys.None)
            {
                // Only consider the right Alt key as part of the hotkey
                return keyCode == Keys.RMenu;
            }

            // For other hotkey combinations, use the original logic
            return (keyCode == HotkeyKey) ||
                  (keyCode == Keys.ControlKey && (HotkeyModifier & Keys.Control) == Keys.Control) ||
                  (keyCode == Keys.Menu && (HotkeyModifier & Keys.Alt) == Keys.Alt) ||
                  (keyCode == Keys.ShiftKey && (HotkeyModifier & Keys.Shift) == Keys.Shift) ||
                  (keyCode == Keys.RMenu && (HotkeyModifier & Keys.Alt) == Keys.Alt);
        }

        public string GetHotkeyDisplayText()
        {
            // Special case for right Alt key to show user-friendly text
            if (HotkeyKey == Keys.RMenu && HotkeyModifier == Keys.None)
            {
                return "RIGHT ALT";
            }
            
            return GetModifiersDisplayText() + HotkeyKey.ToString();
        }

        private string GetModifiersDisplayText()
        {
            string text = "";

            if ((HotkeyModifier & Keys.Control) == Keys.Control) text += "Ctrl + ";
            if ((HotkeyModifier & Keys.Alt) == Keys.Alt) text += "Alt + ";
            if ((HotkeyModifier & Keys.Shift) == Keys.Shift) text += "Shift + ";

            return text;
        }

        public void ProcessKeyDown(Keys keyData)
        {
            if (IsRecordingHotkey)
            {
                // Extract modifiers and key
                recordedModifiers = keyData & Keys.Modifiers;
                recordedKey = keyData & ~Keys.Modifiers;

                // Special case for right Alt key (AltGr)
                if (keyData == Keys.RMenu || keyData == (Keys.LControlKey | Keys.Alt))
                {
                    recordedModifiers = Keys.None;
                    recordedKey = Keys.RMenu;
                }
            }
        }

        public void SetHotkey(Keys key, Keys modifiers)
        {
            try
            {
                HotkeyKey = key;
                HotkeyModifier = modifiers;

                // Convert to settings format
                List<string> modifiersList = new List<string>();
                if ((HotkeyModifier & Keys.Control) == Keys.Control) modifiersList.Add("Control");
                if ((HotkeyModifier & Keys.Alt) == Keys.Alt) modifiersList.Add("Alt");
                if ((HotkeyModifier & Keys.Shift) == Keys.Shift) modifiersList.Add("Shift");

                // Save to settings
                Settings.Instance.HotkeyModifiers = string.Join(", ", modifiersList);
                Settings.Instance.HotkeyKey = HotkeyKey.ToString();
                Settings.Instance.Save();
            }
            catch (Exception)
            {
                // If saving fails, at least keep the values in memory
            }
        }

        public void Dispose()
        {
            if (keyboardHook != null)
            {
                keyboardHook.KeyDown -= KeyboardHook_KeyDown;
                keyboardHook.KeyUp -= KeyboardHook_KeyUp;
                keyboardHook.Dispose();
                keyboardHook = null;
            }
        }
    }
}
