using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.Linq;

namespace PowerVoicr.Services
{
    public class KeyboardHook : IDisposable
    {
        // Constants for keyboard hook
        private const int WH_KEYBOARD_LL = 13;
        private const int WM_KEYDOWN = 0x0100;
        private const int WM_KEYUP = 0x0101;
        private const int WM_SYSKEYDOWN = 0x0104;
        private const int WM_SYSKEYUP = 0x0105;

        // Virtual key codes
        private const int VK_RMENU = 0xA5; // Right Alt key
        private const int VK_LMENU = 0xA4; // Left Alt key
        private const int VK_CONTROL = 0x11; // Control key
        private const int VK_LCONTROL = 0xA2; // Left Control key
        private const int VK_RCONTROL = 0xA3; // Right Control key
        private const int VK_SHIFT = 0x10; // Shift key
        private const int VK_LSHIFT = 0xA0; // Left Shift key
        private const int VK_RSHIFT = 0xA1; // Right Shift key

        [StructLayout(LayoutKind.Sequential)]
        private struct KBDLLHOOKSTRUCT
        {
            public int vkCode;
            public int scanCode;
            public int flags;
            public int time;
            public IntPtr dwExtraInfo;
        }

        private delegate IntPtr LowLevelKeyboardProc(int nCode, IntPtr wParam, IntPtr lParam);
        private LowLevelKeyboardProc _proc;
        private IntPtr _hookID = IntPtr.Zero;

        public event KeyEventHandler KeyDown;
        public event KeyEventHandler KeyUp;

        // Track pressed keys to detect combinations
        private HashSet<int> pressedKeys = new HashSet<int>();

        public KeyboardHook()
        {
            _proc = HookCallback;
        }

        public void Install()
        {
            _hookID = SetHook(_proc);
        }

        public void Uninstall()
        {
            if (_hookID != IntPtr.Zero)
            {
                UnhookWindowsHookEx(_hookID);
                _hookID = IntPtr.Zero;
            }
        }

        private IntPtr SetHook(LowLevelKeyboardProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule curModule = curProcess.MainModule)
            {
                return SetWindowsHookEx(WH_KEYBOARD_LL, proc, GetModuleHandle(curModule.ModuleName), 0);
            }
        }

        private IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0)
            {
                // Get the keyboard data
                KBDLLHOOKSTRUCT kbd = (KBDLLHOOKSTRUCT)Marshal.PtrToStructure(lParam, typeof(KBDLLHOOKSTRUCT));
                int vkCode = kbd.vkCode;

                bool isKeyDown = (wParam == (IntPtr)WM_KEYDOWN || wParam == (IntPtr)WM_SYSKEYDOWN);
                bool isKeyUp = (wParam == (IntPtr)WM_KEYUP || wParam == (IntPtr)WM_SYSKEYUP);

                // Track pressed keys for combination detection
                if (isKeyDown)
                {
                    pressedKeys.Add(vkCode);
                }
                else if (isKeyUp)
                {
                    pressedKeys.Remove(vkCode);
                }

                // Check if this is an Alt key
                bool isAltKey = (vkCode == VK_RMENU || vkCode == VK_LMENU);

                if (isAltKey)
                {
                    Keys key = (vkCode == VK_RMENU) ? Keys.RMenu : Keys.LMenu;

                    // Check if any other keys are currently pressed (modifiers or regular keys)
                    bool hasOtherKeys = pressedKeys.Any(k =>
                        // Other modifier keys
                        k == VK_LCONTROL || k == VK_RCONTROL ||
                        k == VK_LSHIFT || k == VK_RSHIFT ||
                        // Other Alt keys
                        (k != vkCode && (k == VK_LMENU || k == VK_RMENU)) ||
                        // Any non-modifier keys (letters, numbers, function keys, etc.)
                        IsNonModifierKey(k));

                    // Create event args with proper modifier state
                    KeyEventArgs e = new KeyEventArgs(key);

                    // Only trigger if Alt is pressed alone
                    if (!hasOtherKeys)
                    {
                        if (isKeyDown)
                        {
                            KeyDown?.Invoke(this, e);
                        }
                        else if (isKeyUp)
                        {
                            KeyUp?.Invoke(this, e);
                        }

                        if (e.Handled)
                        {
                            return (IntPtr)1; // Handled
                        }
                    }
                }
                else
                {
                    // Regular key processing for non-Alt keys
                    Keys key = (Keys)vkCode;

                    // Include the standard modifier keys
                    KeyEventArgs e = new KeyEventArgs(
                        key |
                        (Control.ModifierKeys & Keys.Shift) |
                        (Control.ModifierKeys & Keys.Control) |
                        (Control.ModifierKeys & Keys.Alt));

                    if (isKeyDown)
                    {
                        KeyDown?.Invoke(this, e);
                    }
                    else if (isKeyUp)
                    {
                        KeyUp?.Invoke(this, e);
                    }

                    if (e.Handled)
                    {
                        return (IntPtr)1; // Handled
                    }
                }
            }

            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        /// <summary>
        /// Determines if a virtual key code represents a non-modifier key
        /// </summary>
        private bool IsNonModifierKey(int vkCode)
        {
            // Exclude modifier keys
            if (vkCode == VK_LCONTROL || vkCode == VK_RCONTROL ||
                vkCode == VK_LSHIFT || vkCode == VK_RSHIFT ||
                vkCode == VK_LMENU || vkCode == VK_RMENU)
            {
                return false;
            }

            // Include alphanumeric keys (A-Z, 0-9)
            if ((vkCode >= 0x30 && vkCode <= 0x39) || // 0-9
                (vkCode >= 0x41 && vkCode <= 0x5A))   // A-Z
            {
                return true;
            }

            // Include function keys (F1-F24)
            if (vkCode >= 0x70 && vkCode <= 0x87)
            {
                return true;
            }

            // Include common special keys that are often used with Alt
            switch (vkCode)
            {
                case 0x09: // Tab
                case 0x0D: // Enter
                case 0x1B: // Escape
                case 0x20: // Space
                case 0x25: // Left Arrow
                case 0x26: // Up Arrow
                case 0x27: // Right Arrow
                case 0x28: // Down Arrow
                case 0x2E: // Delete
                case 0x73: // F4 (for Alt+F4)
                    return true;
                default:
                    return false;
            }
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr SetWindowsHookEx(int idHook, LowLevelKeyboardProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode, IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        private static extern IntPtr GetModuleHandle(string lpModuleName);

        public void Dispose()
        {
            Uninstall();
        }
    }
}
