{"version": "2.0.0", "tasks": [{"label": "build PowerVoicr", "type": "shell", "command": "dotnet", "args": ["build", "PowerVoicr.sln", "--configuration", "Debug"], "group": "build", "problemMatcher": ["$msCompile"], "isBackground": false}, {"label": "run PowerVoicr", "type": "shell", "command": "dotnet", "args": ["run", "--project", "PowerVoicr/PowerVoicr.csproj", "--configuration", "Debug"], "group": "build", "problemMatcher": ["$msCompile"], "isBackground": false, "dependsOn": "build PowerVoicr"}]}