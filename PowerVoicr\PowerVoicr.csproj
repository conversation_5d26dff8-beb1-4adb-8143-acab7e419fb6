<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Speech" Version="9.0.0" />
    <PackageReference Include="NHotkey.WindowsForms" Version="2.1.1" />
    <PackageReference Include="InputSimulator" Version="1.0.4" />
    <PackageReference Include="Microsoft.CognitiveServices.Speech" Version="1.38.0" />
    <PackageReference Include="NAudio" Version="2.2.1" />
  </ItemGroup>
</Project>