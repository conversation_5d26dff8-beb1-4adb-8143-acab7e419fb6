using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace PowerVoicr
{
    public partial class FloatingRecordingIndicator : Form
    {
        private System.Windows.Forms.Timer animationTimer;
        private float pulseOpacity = 0.8f;
        private bool pulseDirection = false; // false = decreasing, true = increasing
        private bool isRecording = false;
        private string currentText = "";

        public FloatingRecordingIndicator()
        {
            InitializeComponent();
            SetupForm();
            SetupAnimation();
        }

        private void SetupForm()
        {
            // Form properties for floating behavior
            this.FormBorderStyle = FormBorderStyle.None;
            this.TopMost = true;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.Manual;
            this.BackColor = Color.Black;
            this.TransparencyKey = Color.Black;
            this.AllowTransparency = true;
            this.Size = new Size(300, 80);

            // Position in top-right corner of screen
            var screen = Screen.PrimaryScreen.WorkingArea;
            this.Location = new Point(screen.Right - this.Width - 20, screen.Top + 20);

            // Make form non-interactive
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            this.SetStyle(ControlStyles.UserPaint, true);

            // Initially hidden
            this.Visible = false;
        }

        private void SetupAnimation()
        {
            animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 50; // 20 FPS
            animationTimer.Tick += AnimationTimer_Tick;
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (isRecording)
            {
                // Pulse animation
                if (pulseDirection)
                {
                    pulseOpacity += 0.05f;
                    if (pulseOpacity >= 1.0f)
                    {
                        pulseOpacity = 1.0f;
                        pulseDirection = false;
                    }
                }
                else
                {
                    pulseOpacity -= 0.05f;
                    if (pulseOpacity <= 0.3f)
                    {
                        pulseOpacity = 0.3f;
                        pulseDirection = true;
                    }
                }

                this.Invalidate();
            }
        }

        public void StartRecording()
        {
            isRecording = true;
            currentText = "";
            this.Visible = true;
            animationTimer.Start();
            this.Invalidate();
        }

        public void StopRecording()
        {
            isRecording = false;
            animationTimer.Stop();
            this.Visible = false;
        }

        public void UpdateTranscription(string text)
        {
            currentText = text;
            this.Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            if (!isRecording) return;

            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

            // Create rounded rectangle background
            Rectangle bgRect = new Rectangle(10, 10, this.Width - 20, this.Height - 20);
            using (GraphicsPath path = CreateRoundedRectangle(bgRect, 15))
            {
                // Background with pulse opacity
                using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb((int)(pulseOpacity * 255), 30, 30, 30)))
                {
                    g.FillPath(bgBrush, path);
                }

                // Border
                using (Pen borderPen = new Pen(Color.FromArgb((int)(pulseOpacity * 255), 255, 100, 100), 2))
                {
                    g.DrawPath(borderPen, path);
                }
            }

            // Recording indicator dot
            int dotSize = 12;
            Rectangle dotRect = new Rectangle(20, (this.Height - dotSize) / 2, dotSize, dotSize);
            using (SolidBrush dotBrush = new SolidBrush(Color.FromArgb((int)(pulseOpacity * 255), 255, 50, 50)))
            {
                g.FillEllipse(dotBrush, dotRect);
            }

            // "Recording..." text
            string recordingText = "Recording...";
            using (Font font = new Font("Segoe UI", 10, FontStyle.Bold))
            using (SolidBrush textBrush = new SolidBrush(Color.FromArgb((int)(pulseOpacity * 255), 255, 255, 255)))
            {
                Rectangle textRect = new Rectangle(40, 15, this.Width - 60, 20);
                g.DrawString(recordingText, font, textBrush, textRect);
            }

            // Current transcription text
            if (!string.IsNullOrEmpty(currentText))
            {
                using (Font font = new Font("Segoe UI", 8))
                using (SolidBrush textBrush = new SolidBrush(Color.FromArgb((int)(pulseOpacity * 255), 200, 200, 200)))
                {
                    Rectangle textRect = new Rectangle(20, 35, this.Width - 40, 30);
                    string displayText = currentText.Length > 40 ? currentText.Substring(0, 37) + "..." : currentText;
                    g.DrawString(displayText, font, textBrush, textRect);
                }
            }
        }

        private GraphicsPath CreateRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            int diameter = radius * 2;

            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            path.CloseFigure();

            return path;
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }

        // Prevent form from being activated/focused
        protected override bool ShowWithoutActivation => true;

        private void InitializeComponent()
        {
            this.SuspendLayout();
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(300, 80);
            this.Name = "FloatingRecordingIndicator";
            this.Text = "Recording Indicator";
            this.ResumeLayout(false);
        }
    }
}
