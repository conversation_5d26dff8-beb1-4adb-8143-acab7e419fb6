{"version": "0.2.0", "configurations": [{"name": "Launch PowerVoicr", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/PowerVoicr/bin/Debug/net9.0-windows/PowerVoicr.exe", "args": [], "cwd": "${workspaceFolder}/PowerVoicr", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false, "env": {"AZURE_SPEECH_KEY": "GJWGpzBYA5KxyS1inuHyFEucJypmr1PkE0QLEXRK70XFVQq1fsRfJQQJ99BGACGhslBXJ3w3AAAYACOG5VPM", "AZURE_SPEECH_REGION": "centralindia", "AZURE_SPEECH_ENDPOINT": "https://centralindia.api.cognitive.microsoft.com/"}}, {"name": "Launch PowerVoicr (External Console)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/PowerVoicr/bin/Debug/net9.0-windows/PowerVoicr.exe", "args": [], "cwd": "${workspaceFolder}/PowerVoicr", "console": "externalTerminal", "stopAtEntry": false, "requireExactSource": false, "env": {"AZURE_SPEECH_KEY": "GJWGpzBYA5KxyS1inuHyFEucJypmr1PkE0QLEXRK70XFVQq1fsRfJQQJ99BGACGhslBXJ3w3AAAYACOG5VPM", "AZURE_SPEECH_REGION": "centralindia", "AZURE_SPEECH_ENDPOINT": "https://centralindia.api.cognitive.microsoft.com/"}}, {"name": "Launch PowerVoicr (Debug Mode)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/PowerVoicr/bin/Debug/net9.0-windows/PowerVoicr.exe", "args": [], "cwd": "${workspaceFolder}/PowerVoicr", "console": "internalConsole", "stopAtEntry": true, "requireExactSource": false, "justMyCode": false, "env": {"AZURE_SPEECH_KEY": "GJWGpzBYA5KxyS1inuHyFEucJypmr1PkE0QLEXRK70XFVQq1fsRfJQQJ99BGACGhslBXJ3w3AAAYACOG5VPM", "AZURE_SPEECH_REGION": "centralindia", "AZURE_SPEECH_ENDPOINT": "https://centralindia.api.cognitive.microsoft.com/"}}, {"name": "Attach to PowerVoicr Process", "type": "coreclr", "request": "attach", "processName": "PowerVoicr.exe"}, {"name": "Test Right Alt Key Detection", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/PowerVoicr/bin/Debug/net9.0-windows/PowerVoicr.exe", "args": ["--diagnostic-mode"], "cwd": "${workspaceFolder}/PowerVoicr", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false}]}