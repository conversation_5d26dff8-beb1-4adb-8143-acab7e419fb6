# Right Alt Key Detection Troubleshooting Guide

## Issue: Right Alt Key Not Detected

### Possible Causes

1. **AltGr Key Interference** - In many keyboard layouts, Right Alt is mapped as AltGr (Alt Graphics)
2. **Keyboard Layout Issues** - Some layouts treat Right Alt differently
3. **Windows Language Settings** - Input language affects key behavior
4. **Keyboard Hook Installation** - Low-level hook might not be properly installed
5. **Administrator Privileges** - Some keyboard hooks require elevated permissions

### Diagnostic Steps

#### Step 1: Check Keyboard Layout
1. Open Windows Settings → Time & Language → Language & Region
2. Check your current input language
3. Note: Languages like German, French, Spanish often use AltGr

#### Step 2: Test Raw Key Detection
Create a simple test to verify the actual virtual key codes being received:

```csharp
// In HotkeyService.cs, add this temporary debug method
private void DebugKeyPress(KeyEventArgs e)
{
    string debug = $"Key: {e.KeyCode}, Virtual: {(int)e.KeyCode:X2}, " +
                   $"Modifiers: {e.Modifiers}, Control.ModifierKeys: {Control.ModifierKeys}";
    
    // Also check direct Win32 API
    short rightAlt = GetAsyncKeyState(0xA5);
    short leftAlt = GetAsyncKeyState(0xA4);
    short leftCtrl = GetAsyncKeyState(0xA2);
    
    debug += $", RightAlt API: {rightAlt:X4}, LeftAlt API: {leftAlt:X4}, LeftCtrl API: {leftCtrl:X4}";
    
    System.Diagnostics.Debug.WriteLine(debug);
    Console.WriteLine(debug); // Also output to console
}
```

#### Step 3: Common Right Alt Key Issues

**Issue**: Right Alt + Left Ctrl pressed together (AltGr)
- **Solution**: Filter out AltGr combinations
- **Code**: Check if both Right Alt and Left Ctrl are pressed simultaneously

**Issue**: Right Alt reported as generic "Menu" key
- **Solution**: Check for Keys.Menu and verify with Win32 API
- **Code**: Use GetAsyncKeyState(0xA5) to confirm right Alt specifically

**Issue**: Keyboard layout remapping
- **Solution**: Test with US English keyboard layout
- **Code**: Add keyboard layout detection

### Solutions

#### Solution 1: Enhanced Right Alt Detection (Implemented)
```csharp
// Uses direct Win32 API calls to bypass Windows key mapping issues
if (EnhancedRightAltDetector.IsRightAltOnlyPressed())
{
    // Right Alt detected without other modifiers
}
```

#### Solution 2: Administrator Privileges
1. Right-click PowerVoicr.exe
2. Select "Run as administrator"
3. Test right Alt functionality

#### Solution 3: Change Keyboard Layout Temporarily
1. Add "English (United States)" keyboard layout
2. Switch to US layout: Win + Space
3. Test right Alt in US layout

#### Solution 4: Alternative Hotkey
If right Alt continues to have issues, consider using:
- **Right Ctrl key** (`Keys.RControlKey`)
- **Right Shift key** (`Keys.RShiftKey`) 
- **F12 key** for testing

#### Solution 5: Disable AltGr
For layouts with AltGr, disable it temporarily:
1. Open Registry Editor (regedit)
2. Navigate to: `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Keyboard Layout`
3. Create DWORD: `Scancode Map`
4. Value: `00000000000000000300000000E038000000000000000000`
5. Restart computer

### Testing Commands

#### Debug Output Monitoring
1. Run application from command line to see debug output
2. Press various keys and observe output
3. Look for pattern differences between left and right Alt

#### Windows Event Viewer
1. Open Event Viewer
2. Check Windows Logs → Application
3. Look for PowerVoicr-related entries

### Expected Debug Output

**Correct Right Alt Detection:**
```
KeyDown: RMenu, Modifiers: None, Control.ModifierKeys: None
Right Alt detection - API check: True
Right Alt detected - activating hotkey!
```

**AltGr Interference:**
```
KeyDown: RMenu, Modifiers: None, Control.ModifierKeys: Control
Right Alt detection - API check: False (AltGr detected)
```

**Keyboard Layout Issue:**
```
KeyDown: Menu, Modifiers: None, Control.ModifierKeys: None
Generic Menu key detected. Right Alt API check: True
Right Alt detected - activating hotkey!
```

### If All Else Fails

1. **Use Left Alt temporarily** - Change setting to `"HotkeyKey": "LMenu"`
2. **Use different key** - Try F12 or Right Ctrl
3. **Report keyboard info** - Include keyboard model, Windows version, language settings

### Collecting Debug Information

Run this PowerShell command to collect system info:
```powershell
Get-WmiObject -Class Win32_Keyboard | Select-Object Name, Layout
Get-Culture | Select-Object DisplayName, KeyboardLayoutId
[System.Windows.Forms.Application]::CurrentInputLanguage
```

This will help identify keyboard layout-specific issues affecting right Alt detection.
