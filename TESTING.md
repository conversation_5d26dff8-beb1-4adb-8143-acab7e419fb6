# PowerVoicr Right Alt Key Configuration Test

## Test Instructions

1. **Build the application**: Use Ctrl+Shift+P → "Tasks: Run Task" → "build PowerVoicr"
2. **Start debugging**: Press F5 or use "Launch PowerVoicr" configuration
3. **Test Right Alt activation**:
   - Press and hold the **Right Alt key** (should activate recording)
   - Speak a test phrase
   - Release the Right Alt key (should stop recording and type text)
4. **Test Left Alt does NOT activate**:
   - Press and hold the **Left Alt key** (should NOT activate recording)
   - Verify no floating indicator appears

## Expected Behavior

### ✅ Right Alt Key
- **Press Right Alt**: Floating recording indicator appears
- **Hold Right Alt**: Real-time transcription shows your speech
- **Release Right Alt**: Text is typed into active application

### ❌ Left Alt Key
- **Press Left Alt**: No recording indicator
- **Hold Left Alt**: No speech recognition
- **Release Left Alt**: No text input

## Configuration Details

- **Activation Key**: Right Alt only (`Keys.RMenu`)
- **Azure Speech Region**: Central India
- **Display Name**: "RIGHT ALT"
- **Left Alt Behavior**: Disabled/Ignored

## Troubleshooting

If Right Alt doesn't work:
1. Check Settings.json contains `"HotkeyKey": "RMenu"`
2. Verify Azure Speech credentials are configured
3. Ensure microphone permissions are granted
4. Check Windows language settings match speech recognition language

If Left Alt activates (bug):
1. Review HotkeyService.cs changes
2. Verify IsHotkeyPressed method only checks for Keys.RMenu
3. Check IsPartOfHotkey method excludes Keys.LMenu

## Test Results

- [ ] Right Alt activates recording
- [ ] Left Alt does NOT activate recording  
- [ ] Alt+Other key combinations do NOT activate
- [ ] Instructions show "RIGHT ALT"
- [ ] Speech recognition works with Central India Azure endpoint
- [ ] Text is properly typed into applications
