using System;

namespace PowerVoicr.Services.Interfaces
{
    public interface ISpeechRecognitionService : IDisposable
    {
        event EventHandler<string> TextRecognized;
        event EventHandler<string> RecognitionInProgress;
        event EventHandler<string> RecognitionCanceled;

        bool IsConfigured { get; }
        bool IsListening { get; }

        Task StartListeningAsync();
        Task StopListeningAsync();
        Task InitializeAsync();
        void AddPhraseToGrammar(string phrase);
        bool IsHealthy();
    }
}
