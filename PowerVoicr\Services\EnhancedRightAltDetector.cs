using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace PowerVoicr.Services
{
    /// <summary>
    /// Enhanced Right Alt key detector that handles AltGr and various keyboard layouts
    /// </summary>
    public class EnhancedRightAltDetector
    {
        // Win32 API imports
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern short GetKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern uint MapVirtualKey(uint uCode, uint uMapType);

        [DllImport("user32.dll")]
        private static extern int GetKeyboardLayout(uint thread);

        // Virtual key codes
        private const int VK_RMENU = 0xA5;  // Right Alt
        private const int VK_LMENU = 0xA4;  // Left Alt
        private const int VK_LCONTROL = 0xA2; // Left Control
        private const int VK_RCONTROL = 0xA3; // Right Control
        private const int VK_CONTROL = 0x11;  // Generic Control
        private const int VK_SHIFT = 0x10;    // Shift

        public static bool IsRightAltPressed()
        {
            short rightAltState = GetAsyncKeyState(VK_RMENU);
            return (rightAltState & 0x8000) != 0;
        }

        public static bool IsLeftAltPressed()
        {
            short leftAltState = GetAsyncKeyState(VK_LMENU);
            return (leftAltState & 0x8000) != 0;
        }

        public static bool IsRightAltOnlyPressed()
        {
            // Check if right Alt is pressed
            if (!IsRightAltPressed())
                return false;

            // Check if other modifier keys are NOT pressed
            bool leftAltPressed = IsLeftAltPressed();
            bool ctrlPressed = (GetAsyncKeyState(VK_CONTROL) & 0x8000) != 0;
            bool shiftPressed = (GetAsyncKeyState(VK_SHIFT) & 0x8000) != 0;

            // In some layouts, AltGr = Right Alt + Left Control
            // We need to handle this case specially
            bool leftCtrlPressed = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
            bool rightCtrlPressed = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

            // If AltGr is detected (Right Alt + Left Control), we should NOT activate
            bool isAltGr = leftCtrlPressed && IsRightAltPressed();

            return !leftAltPressed && !ctrlPressed && !shiftPressed && !isAltGr;
        }

        public static string GetKeyboardLayoutInfo()
        {
            int layout = GetKeyboardLayout(0);
            return $"Keyboard Layout: 0x{layout:X8}";
        }

        public static string GetDetailedKeyState()
        {
            var rightAlt = GetAsyncKeyState(VK_RMENU);
            var leftAlt = GetAsyncKeyState(VK_LMENU);
            var leftCtrl = GetAsyncKeyState(VK_LCONTROL);
            var rightCtrl = GetAsyncKeyState(VK_RCONTROL);
            var shift = GetAsyncKeyState(VK_SHIFT);

            return $@"Key States:
├─ Right Alt (VK_RMENU): 0x{rightAlt:X4} {((rightAlt & 0x8000) != 0 ? "PRESSED" : "Released")}
├─ Left Alt (VK_LMENU): 0x{leftAlt:X4} {((leftAlt & 0x8000) != 0 ? "PRESSED" : "Released")}
├─ Left Ctrl: 0x{leftCtrl:X4} {((leftCtrl & 0x8000) != 0 ? "PRESSED" : "Released")}
├─ Right Ctrl: 0x{rightCtrl:X4} {((rightCtrl & 0x8000) != 0 ? "PRESSED" : "Released")}
├─ Shift: 0x{shift:X4} {((shift & 0x8000) != 0 ? "PRESSED" : "Released")}
├─ AltGr Detected: {(((rightAlt & 0x8000) != 0) && ((leftCtrl & 0x8000) != 0))}
└─ Right Alt Only: {IsRightAltOnlyPressed()}";
        }
    }
}
