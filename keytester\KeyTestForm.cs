using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace KeyTester
{
    public partial class KeyTestForm : Form
    {
        // Win32 API imports
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern short GetKeyState(int vKey);

        // Virtual key codes
        private const int VK_RMENU = 0xA5;    // Right Alt
        private const int VK_LMENU = 0xA4;    // Left Alt
        private const int VK_LCONTROL = 0xA2; // Left Control
        private const int VK_RCONTROL = 0xA3; // Right Control
        private const int VK_CONTROL = 0x11;  // Generic Control
        private const int VK_SHIFT = 0x10;    // Shift

        private ListBox logListBox;
        private Label instructionLabel;
        private System.Windows.Forms.Timer updateTimer;

        public KeyTestForm()
        {
            InitializeComponent();
            SetupTimer();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings
            this.Text = "Key Tester - Press Right Alt Key";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.KeyPreview = true;

            // Instruction label
            instructionLabel = new Label();
            instructionLabel.Text = "Press and release the RIGHT ALT key to see what key codes are generated.\n" +
                                   "The log below will show all key events and Win32 API states.";
            instructionLabel.Location = new Point(10, 10);
            instructionLabel.Size = new Size(560, 50);
            instructionLabel.ForeColor = Color.Blue;
            this.Controls.Add(instructionLabel);

            // Log text box (editable for copying)
            logListBox = new ListBox();
            logListBox.Location = new Point(10, 70);
            logListBox.Size = new Size(560, 380);
            logListBox.Font = new Font("Consolas", 9);
            logListBox.SelectionMode = SelectionMode.MultiExtended;
            this.Controls.Add(logListBox);
            
            // Add context menu for copying
            var contextMenu = new ContextMenuStrip();
            var copyItem = new ToolStripMenuItem("Copy Selected");
            copyItem.Click += (s, e) => {
                if (logListBox.SelectedItems.Count > 0)
                {
                    var selectedText = string.Join(Environment.NewLine, 
                        logListBox.SelectedItems.Cast<string>());
                    Clipboard.SetText(selectedText);
                }
            };
            var copyAllItem = new ToolStripMenuItem("Copy All");
            copyAllItem.Click += (s, e) => {
                var allText = string.Join(Environment.NewLine, 
                    logListBox.Items.Cast<string>());
                Clipboard.SetText(allText);
            };
            contextMenu.Items.Add(copyItem);
            contextMenu.Items.Add(copyAllItem);
            logListBox.ContextMenuStrip = contextMenu;

            // Event handlers
            this.KeyDown += KeyTestForm_KeyDown;
            this.KeyUp += KeyTestForm_KeyUp;

            this.ResumeLayout(false);

            // Initial log
            LogMessage("=== Key Tester Started ===");
            LogMessage("Press RIGHT ALT key to test detection...");
            LogMessage("");
        }

        private void SetupTimer()
        {
            updateTimer = new System.Windows.Forms.Timer();
            updateTimer.Interval = 100; // Update every 100ms
            updateTimer.Tick += UpdateTimer_Tick;
            updateTimer.Start();
        }

        private void UpdateTimer_Tick(object? sender, EventArgs e)
        {
            // Continuously monitor Win32 key states
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            bool leftCtrl = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
            bool rightCtrl = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;
            bool genericCtrl = (GetAsyncKeyState(VK_CONTROL) & 0x8000) != 0;
            bool shift = (GetAsyncKeyState(VK_SHIFT) & 0x8000) != 0;

            // Update title bar with current state
            string status = $"RightAlt:{rightAlt} LeftAlt:{leftAlt} LeftCtrl:{leftCtrl} RightCtrl:{rightCtrl} GenericCtrl:{genericCtrl} Shift:{shift}";
            if (this.Text != $"Key Tester - {status}")
            {
                this.Text = $"Key Tester - {status}";
            }
        }

        private void KeyTestForm_KeyDown(object sender, KeyEventArgs e)
        {
            string keyInfo = $"KeyDown: {e.KeyCode} (0x{(int)e.KeyCode:X2})";
            string modifiers = $"Modifiers: {e.Modifiers}";
            string controlModifiers = $"Control.ModifierKeys: {Control.ModifierKeys}";
            
            // Get Win32 API states
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            bool leftCtrl = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
            bool rightCtrl = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;
            bool genericCtrl = (GetAsyncKeyState(VK_CONTROL) & 0x8000) != 0;
            bool shift = (GetAsyncKeyState(VK_SHIFT) & 0x8000) != 0;

            string apiStates = $"Win32 API - RightAlt:{rightAlt} LeftAlt:{leftAlt} LeftCtrl:{leftCtrl} RightCtrl:{rightCtrl} GenericCtrl:{genericCtrl} Shift:{shift}";

            LogMessage($">>> {keyInfo}");
            LogMessage($"    {modifiers}");
            LogMessage($"    {controlModifiers}");
            LogMessage($"    {apiStates}");

            // Special analysis for right Alt key detection
            if (e.KeyCode == Keys.LControlKey && rightAlt)
            {
                LogMessage($"    *** ANALYSIS: LControlKey detected when RightAlt is pressed! ***");
                LogMessage($"    *** This confirms Right Alt is being reported as LControlKey ***");
            }
            else if (e.KeyCode == Keys.RMenu)
            {
                LogMessage($"    *** ANALYSIS: Standard RMenu detected ***");
            }
            else if (e.KeyCode == Keys.Menu)
            {
                LogMessage($"    *** ANALYSIS: Generic Menu key detected ***");
            }

            LogMessage("");
        }

        private void KeyTestForm_KeyUp(object sender, KeyEventArgs e)
        {
            string keyInfo = $"KeyUp: {e.KeyCode} (0x{(int)e.KeyCode:X2})";
            
            // Get Win32 API states
            bool rightAlt = (GetAsyncKeyState(VK_RMENU) & 0x8000) != 0;
            bool leftAlt = (GetAsyncKeyState(VK_LMENU) & 0x8000) != 0;
            bool leftCtrl = (GetAsyncKeyState(VK_LCONTROL) & 0x8000) != 0;
            bool rightCtrl = (GetAsyncKeyState(VK_RCONTROL) & 0x8000) != 0;

            string apiStates = $"Win32 API - RightAlt:{rightAlt} LeftAlt:{leftAlt} LeftCtrl:{leftCtrl} RightCtrl:{rightCtrl}";

            LogMessage($"<<< {keyInfo}");
            LogMessage($"    {apiStates}");
            LogMessage("");
        }

        private void LogMessage(string message)
        {
            if (logListBox.InvokeRequired)
            {
                logListBox.Invoke(new Action(() => LogMessage(message)));
                return;
            }

            logListBox.Items.Add($"{DateTime.Now:HH:mm:ss.fff} {message}");
            logListBox.TopIndex = logListBox.Items.Count - 1; // Auto-scroll to bottom
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                updateTimer?.Stop();
                updateTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
