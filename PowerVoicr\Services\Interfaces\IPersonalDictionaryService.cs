using System;
using System.Collections.Generic;

namespace PowerVoicr.Services.Interfaces
{
    public interface IPersonalDictionaryService : IDisposable
    {
        /// <summary>
        /// Event fired when a new word is learned
        /// </summary>
        event EventHandler<string> WordLearned;
        
        /// <summary>
        /// Event fired when a word is corrected
        /// </summary>
        event EventHandler<WordCorrectionEventArgs> WordCorrected;
        
        /// <summary>
        /// Gets all words in the personal dictionary
        /// </summary>
        IReadOnlyList<PersonalDictionaryEntry> GetAllWords();
        
        /// <summary>
        /// Adds a word to the personal dictionary
        /// </summary>
        void AddWord(string word, string pronunciation = "", WordCategory category = WordCategory.General);
        
        /// <summary>
        /// Removes a word from the personal dictionary
        /// </summary>
        bool RemoveWord(string word);
        
        /// <summary>
        /// Checks if a word exists in the personal dictionary
        /// </summary>
        bool ContainsWord(string word);
        
        /// <summary>
        /// Gets suggestions for a word based on phonetic similarity
        /// </summary>
        IList<string> GetSuggestions(string word, int maxSuggestions = 5);
        
        /// <summary>
        /// Records a correction made by the user
        /// </summary>
        void RecordCorrection(string originalWord, string correctedWord);
        
        /// <summary>
        /// Gets the most common corrections for a word
        /// </summary>
        IList<string> GetCommonCorrections(string word);
        
        /// <summary>
        /// Learns from user input patterns
        /// </summary>
        void LearnFromText(string text);
        
        /// <summary>
        /// Exports the dictionary to a file
        /// </summary>
        void ExportDictionary(string filePath);
        
        /// <summary>
        /// Imports dictionary from a file
        /// </summary>
        void ImportDictionary(string filePath);
        
        /// <summary>
        /// Gets statistics about the dictionary
        /// </summary>
        PersonalDictionaryStats GetStatistics();
    }
    
    public class PersonalDictionaryEntry
    {
        public string Word { get; set; } = "";
        public string Pronunciation { get; set; } = "";
        public WordCategory Category { get; set; } = WordCategory.General;
        public int UsageCount { get; set; } = 0;
        public DateTime DateAdded { get; set; } = DateTime.Now;
        public DateTime LastUsed { get; set; } = DateTime.Now;
        public double Confidence { get; set; } = 1.0;
    }
    
    public enum WordCategory
    {
        General,
        Name,
        TechnicalTerm,
        Abbreviation,
        Company,
        Location,
        Custom
    }
    
    public class WordCorrectionEventArgs : EventArgs
    {
        public string OriginalWord { get; set; } = "";
        public string CorrectedWord { get; set; } = "";
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
    
    public class PersonalDictionaryStats
    {
        public int TotalWords { get; set; }
        public int TotalUsages { get; set; }
        public Dictionary<WordCategory, int> WordsByCategory { get; set; } = new();
        public List<PersonalDictionaryEntry> MostUsedWords { get; set; } = new();
        public List<PersonalDictionaryEntry> RecentlyAddedWords { get; set; } = new();
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
