# PowerVoicr - AI-Powered Voice Dictation

PowerVoicr is an advanced voice dictation application inspired by WisprFlow.ai, designed to provide seamless, AI-enhanced speech-to-text functionality across all applications on Windows.

## 🚀 Features

### ✅ Implemented
- **Right Alt-Key Activation**: Press and hold Right Alt key alone to start recording
- **Smart Key Detection**: Only activates on single Right Alt press, not Alt+other key combinations
- **Floating Recording Indicator**: Beautiful, animated UI showing recording status
- **Real-time Transcription**: Live preview of speech recognition
- **Azure Speech Integration**: High-quality speech recognition
- **Universal Input**: Works in any application with text input
- **System Tray Integration**: Runs quietly in the background

### 🔄 In Development
- AI-powered text enhancement and grammar correction
- Personal dictionary for custom words
- Multi-language support
- Context-aware formatting
- Advanced voice commands

## 🎯 Quick Start

### Prerequisites
- Windows 10/11
- .NET 9.0 or later
- Microphone access
- Azure Speech Services key (optional, for enhanced accuracy)

### Installation
1. Clone the repository
2. Open `PowerVoicr.sln` in Visual Studio or VS Code
3. Build and run the project

### Usage
1. **Start the application** - PowerVoicr will minimize to system tray
2. **Press and hold Right Alt key** (alone, not with other keys) to start recording
3. **Speak clearly** - see real-time transcription in the floating indicator
4. **Release Right Alt key** - text will be automatically typed into the active application

## 🏗️ Architecture

PowerVoicr follows a modular, service-oriented architecture:

- **UI Layer**: Main form, floating indicator, configuration dialogs
- **Service Layer**: Speech recognition, hotkey detection, input simulation
- **Core Components**: Keyboard hooks, audio capture, text processing
- **External Services**: Azure Speech, OpenAI (planned)
- **Data Layer**: Settings, personal dictionary, user profiles

See [docs/architecture.mmd](docs/architecture.mmd) for detailed architecture diagram.

## 📋 Documentation

- [Product Requirements Document](docs/PRD.md) - Detailed feature specifications
- [Architecture Diagram](docs/architecture.mmd) - System architecture overview

## 🔧 Configuration

### Azure Speech Services (Recommended)
1. Create an Azure Speech Services resource
2. Right-click PowerVoicr tray icon → "Azure Configuration"
3. Enter your API key and region
4. Save settings

### Hotkey Customization
- Default: Alt key only
- Customizable through the main interface
- Supports complex key combinations

## 🛠️ Development

### Project Structure
```
PowerVoicr/
├── docs/                   # Documentation
├── Services/              # Core services
│   ├── Interfaces/        # Service contracts
│   ├── HotkeyService.cs   # Keyboard input handling
│   ├── SpeechService.cs   # Speech recognition
│   └── ...
├── FloatingRecordingIndicator.cs  # Recording UI
├── Form1.cs              # Main application form
├── Settings.cs           # Configuration management
└── Program.cs            # Application entry point
```

### Key Components

#### HotkeyService
- Global keyboard hook for Alt key detection
- Filters out Alt+other key combinations
- Precise single-key activation logic

#### FloatingRecordingIndicator
- Animated recording status display
- Real-time transcription preview
- Non-intrusive, always-on-top design

#### SpeechRecognitionService
- Azure Speech Services integration
- Real-time continuous recognition
- Error handling and fallback options

## 🎨 UI/UX Design

PowerVoicr features a modern, sci-fi inspired interface:
- **Dark theme** with neon accents
- **Particle effects** and smooth animations
- **Minimal floating indicator** during recording
- **System tray integration** for background operation

## 🔒 Privacy & Security

- **Local processing** options available
- **Encrypted settings** storage
- **No data collection** without explicit consent
- **Azure Speech data** handled according to Microsoft's privacy policy

## 🚧 Roadmap

### Phase 2: Enhanced Speech Recognition
- [ ] Optimize Azure Speech configuration
- [ ] Add personal dictionary
- [ ] Implement real-time transcription improvements

### Phase 3: AI-Powered Text Enhancement
- [ ] OpenAI integration for text improvement
- [ ] Grammar correction and formatting
- [ ] Context-aware text processing

### Phase 4: Universal Application Integration
- [ ] Enhanced input simulation
- [ ] Application-specific formatting
- [ ] Clipboard integration options

### Phase 5: Advanced Features
- [ ] Multi-language support
- [ ] Custom voice commands
- [ ] Team collaboration features

## 🤝 Contributing

Contributions are welcome! Please read our contributing guidelines and submit pull requests for any improvements.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by [WisprFlow.ai](https://wisprflow.ai/)
- Built with Azure Speech Services
- Uses .NET 9.0 and Windows Forms

## 📞 Support

For support, feature requests, or bug reports, please open an issue on GitHub.

---

**PowerVoicr** - Speak at the speed of thought 🎤✨
