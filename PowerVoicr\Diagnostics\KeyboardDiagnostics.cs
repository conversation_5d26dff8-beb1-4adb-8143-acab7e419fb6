using System;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace PowerVoicr.Diagnostics
{
    public class KeyboardDiagnostics : Form
    {
        private Label lblStatus;
        private Label lblKeyInfo;
        private Label lblModifiers;
        private System.Windows.Forms.Timer updateTimer;

        // Win32 API
        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern short GetKeyState(int vKey);

        // Virtual key codes
        private const int VK_RMENU = 0xA5; // Right Alt
        private const int VK_LMENU = 0xA4; // Left Alt
        private const int VK_CONTROL = 0x11;
        private const int VK_SHIFT = 0x10;

        public KeyboardDiagnostics()
        {
            InitializeComponent();
            SetupTimer();
        }

        private void InitializeComponent()
        {
            this.Text = "PowerVoicr - Right Alt Key Diagnostics";
            this.Size = new System.Drawing.Size(500, 300);
            this.StartPosition = FormStartPosition.CenterScreen;

            lblStatus = new Label
            {
                Text = "Press and hold the Right Alt key...",
                Location = new System.Drawing.Point(20, 20),
                Size = new System.Drawing.Size(450, 30),
                Font = new System.Drawing.Font("Arial", 12, System.Drawing.FontStyle.Bold)
            };

            lblKeyInfo = new Label
            {
                Text = "Key State: None",
                Location = new System.Drawing.Point(20, 60),
                Size = new System.Drawing.Size(450, 100),
                Font = new System.Drawing.Font("Consolas", 10)
            };

            lblModifiers = new Label
            {
                Text = "Modifiers: None",
                Location = new System.Drawing.Point(20, 170),
                Size = new System.Drawing.Size(450, 80),
                Font = new System.Drawing.Font("Consolas", 10)
            };

            this.Controls.Add(lblStatus);
            this.Controls.Add(lblKeyInfo);
            this.Controls.Add(lblModifiers);
        }

        private void SetupTimer()
        {
            updateTimer = new System.Windows.Forms.Timer();
            updateTimer.Interval = 50; // Update every 50ms
            updateTimer.Tick += UpdateTimer_Tick;
            updateTimer.Start();
        }

        private void UpdateTimer_Tick(object sender, EventArgs e)
        {
            // Check Right Alt key state
            short rightAltAsync = GetAsyncKeyState(VK_RMENU);
            short rightAltState = GetKeyState(VK_RMENU);
            short leftAltAsync = GetAsyncKeyState(VK_LMENU);
            short leftAltState = GetKeyState(VK_LMENU);

            bool rightAltPressed = (rightAltAsync & 0x8000) != 0;
            bool leftAltPressed = (leftAltAsync & 0x8000) != 0;

            // Check other modifiers
            bool ctrlPressed = (GetAsyncKeyState(VK_CONTROL) & 0x8000) != 0;
            bool shiftPressed = (GetAsyncKeyState(VK_SHIFT) & 0x8000) != 0;

            // Update status
            if (rightAltPressed)
            {
                lblStatus.Text = "✅ RIGHT ALT DETECTED!";
                lblStatus.ForeColor = System.Drawing.Color.Green;
            }
            else if (leftAltPressed)
            {
                lblStatus.Text = "❌ Left Alt detected (should use Right Alt)";
                lblStatus.ForeColor = System.Drawing.Color.Orange;
            }
            else
            {
                lblStatus.Text = "⭕ No Alt key pressed";
                lblStatus.ForeColor = System.Drawing.Color.Gray;
            }

            // Update key information
            lblKeyInfo.Text = $@"Right Alt Key State:
├─ GetAsyncKeyState: 0x{rightAltAsync:X4} ({(rightAltPressed ? "PRESSED" : "Released")})
├─ GetKeyState: 0x{rightAltState:X4}
├─ VK_RMENU Code: 0x{VK_RMENU:X2} ({VK_RMENU})

Left Alt Key State:
├─ GetAsyncKeyState: 0x{leftAltAsync:X4} ({(leftAltPressed ? "PRESSED" : "Released")})
├─ GetKeyState: 0x{leftAltState:X4}
├─ VK_LMENU Code: 0x{VK_LMENU:X2} ({VK_LMENU})";

            // Update modifier information
            lblModifiers.Text = $@"Current Modifiers:
├─ Right Alt: {(rightAltPressed ? "PRESSED" : "Released")}
├─ Left Alt: {(leftAltPressed ? "PRESSED" : "Released")}
├─ Ctrl: {(ctrlPressed ? "PRESSED" : "Released")}
├─ Shift: {(shiftPressed ? "PRESSED" : "Released")}
├─ Control.ModifierKeys: {Control.ModifierKeys}";
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            updateTimer?.Stop();
            updateTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
