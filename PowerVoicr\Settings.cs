using System.Text.Json;

namespace PowerVoicr
{
    public class Settings
    {
        private static readonly string SettingsFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "PowerVoicr",
            "settings.json");

        public string AzureSpeechKey { get; set; } = "GJWGpzBYA5KxyS1inuHyFEucJypmr1PkE0QLEXRK70XFVQq1fsRfJQQJ99BGACGhslBXJ3w3AAAYACOG5VPM";
        public string AzureSpeechRegion { get; set; } = "centralindia";
        public string AzureSpeechEndpoint { get; set; } = "https://centralindia.api.cognitive.microsoft.com/";

        // OpenAI Settings
        public string OpenAIApiKey { get; set; } = "";
        public string OpenAIModel { get; set; } = "gpt-3.5-turbo";
        public bool EnableTextEnhancement { get; set; } = true;
        public string HotkeyModifiers { get; set; } = "";
        public string HotkeyKey { get; set; } = "RMenu";

        // Singleton instance
        private static Settings? _instance;
        public static Settings Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = Load();
                }
                return _instance;
            }
        }

        public static Settings Load()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(SettingsFilePath)!);

                if (File.Exists(SettingsFilePath))
                {
                    string json = File.ReadAllText(SettingsFilePath);
                    var settings = JsonSerializer.Deserialize<Settings>(json);
                    return settings ?? new Settings();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}", "Settings Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return new Settings();
        }

        public void Save()
        {
            try
            {
                Directory.CreateDirectory(Path.GetDirectoryName(SettingsFilePath)!);
                string json = JsonSerializer.Serialize(this, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(SettingsFilePath, json);
                _instance = this;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Settings Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}