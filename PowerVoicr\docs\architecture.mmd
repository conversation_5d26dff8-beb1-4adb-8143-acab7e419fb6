graph TB
    %% User Interface Layer
    subgraph "UI Layer"
        MainForm[Main Form<br/>Recording Interface]
        TrayIcon[System Tray Icon]
        ConfigForm[Configuration Form]
        FloatingUI[Floating Recording UI]
    end
    
    %% Service Layer
    subgraph "Service Layer"
        ServiceProvider[Service Provider<br/>DI Container]
        HotkeyService[Hotkey Service<br/>Alt Key Detection]
        SpeechService[Speech Recognition Service<br/>Azure Integration]
        CommandService[Command Service<br/>Voice Commands]
        InputService[Input Simulation Service<br/>Text Injection]
        AIService[AI Processing Service<br/>Auto-editing & Formatting]
    end
    
    %% Core Components
    subgraph "Core Components"
        KeyboardHook[Keyboard Hook<br/>Global Key Monitoring]
        AudioCapture[Audio Capture<br/>Microphone Input]
        TextProcessor[Text Processor<br/>Grammar & Formatting]
        AppDetector[Application Detector<br/>Context Awareness]
    end
    
    %% External Services
    subgraph "External Services"
        AzureSpeech[Azure Speech Services<br/>Speech-to-Text]
        OpenAI[OpenAI API<br/>Text Enhancement]
        LocalSTT[Local STT Engine<br/>Offline Processing]
    end
    
    %% Data Layer
    subgraph "Data Layer"
        Settings[Settings Manager<br/>User Preferences]
        PersonalDict[Personal Dictionary<br/>Custom Words]
        CommandRegistry[Command Registry<br/>Voice Commands]
        UserProfiles[User Profiles<br/>Multi-user Support]
    end
    
    %% System Integration
    subgraph "System Integration"
        WindowsAPI[Windows API<br/>System Hooks]
        Clipboard[Clipboard Manager<br/>Copy/Paste]
        ProcessMonitor[Process Monitor<br/>Active App Detection]
    end
    
    %% User Interactions
    User((User)) --> MainForm
    User --> TrayIcon
    User --> FloatingUI
    
    %% UI to Service connections
    MainForm --> ServiceProvider
    ConfigForm --> ServiceProvider
    FloatingUI --> ServiceProvider
    TrayIcon --> ServiceProvider
    
    %% Service Provider connections
    ServiceProvider --> HotkeyService
    ServiceProvider --> SpeechService
    ServiceProvider --> CommandService
    ServiceProvider --> InputService
    ServiceProvider --> AIService
    
    %% Hotkey Service flow
    HotkeyService --> KeyboardHook
    KeyboardHook --> WindowsAPI
    HotkeyService --> SpeechService
    
    %% Speech Recognition flow
    SpeechService --> AudioCapture
    AudioCapture --> AzureSpeech
    AudioCapture --> LocalSTT
    SpeechService --> AIService
    
    %% AI Processing flow
    AIService --> OpenAI
    AIService --> TextProcessor
    TextProcessor --> InputService
    
    %% Input Simulation flow
    InputService --> AppDetector
    InputService --> Clipboard
    InputService --> WindowsAPI
    AppDetector --> ProcessMonitor
    
    %% Command Processing flow
    CommandService --> CommandRegistry
    CommandService --> InputService
    
    %% Data persistence
    Settings --> PersonalDict
    Settings --> CommandRegistry
    Settings --> UserProfiles
    
    %% Alt Key Activation Flow (Highlighted)
    User -.->|"Press Alt Key Only"| HotkeyService
    HotkeyService -.->|"Start Recording"| FloatingUI
    FloatingUI -.->|"Visual Feedback"| User
    SpeechService -.->|"Real-time Transcription"| FloatingUI
    User -.->|"Release Alt Key"| HotkeyService
    HotkeyService -.->|"Stop & Process"| AIService
    AIService -.->|"Enhanced Text"| InputService
    InputService -.->|"Type Text"| User
    
    %% Styling
    classDef userInterface fill:#e1f5fe
    classDef service fill:#f3e5f5
    classDef core fill:#fff3e0
    classDef external fill:#e8f5e8
    classDef data fill:#fce4ec
    classDef system fill:#f1f8e9
    
    class MainForm,TrayIcon,ConfigForm,FloatingUI userInterface
    class ServiceProvider,HotkeyService,SpeechService,CommandService,InputService,AIService service
    class KeyboardHook,AudioCapture,TextProcessor,AppDetector core
    class AzureSpeech,OpenAI,LocalSTT external
    class Settings,PersonalDict,CommandRegistry,UserProfiles data
    class WindowsAPI,Clipboard,ProcessMonitor system
