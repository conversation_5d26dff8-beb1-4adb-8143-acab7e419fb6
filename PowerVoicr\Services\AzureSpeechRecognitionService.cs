using Microsoft.CognitiveServices.Speech;
using Microsoft.CognitiveServices.Speech.Audio;
using PowerVoicr.Services.Interfaces;
using System;
using System.Threading.Tasks;

namespace PowerVoicr.Services
{
    public class AzureSpeechRecognitionService : ISpeechRecognitionService
    {
        private SpeechRecognizer? speechRecognizer;
        private bool isListening = false;
        private bool continuousRecognition = false;
        private DateTime lastRecognitionTime = DateTime.MinValue;
        private int consecutiveErrors = 0;
        private const int MAX_CONSECUTIVE_ERRORS = 3;

        public event EventHandler<string>? TextRecognized;
        public event EventHandler<string>? RecognitionInProgress;
        public event EventHandler<string>? RecognitionCanceled;

        public bool IsConfigured { get; private set; }
        public bool IsListening => isListening;

        public async Task InitializeAsync()
        {
            try
            {
                // Check if Azure Speech key is configured
                string azureKey = Settings.Instance.AzureSpeechKey;
                string azureRegion = Settings.Instance.AzureSpeechRegion;

                if (string.IsNullOrEmpty(azureKey))
                {
                    IsConfigured = false;
                    return;
                }

                // Configure Azure speech recognizer with optimizations
                var speechConfig = SpeechConfig.FromSubscription(azureKey, azureRegion);
                speechConfig.SpeechRecognitionLanguage = "en-US";

                // Performance optimizations for real-time recognition
                speechConfig.OutputFormat = OutputFormat.Detailed;
                speechConfig.SetProfanity(ProfanityOption.Raw); // Avoid filtering delays

                // Advanced performance settings
                speechConfig.SetProperty(PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "3000");
                speechConfig.SetProperty(PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "500");
                speechConfig.SetProperty(PropertyId.Speech_SegmentationSilenceTimeoutMs, "200");
                speechConfig.SetProperty(PropertyId.SpeechServiceResponse_RequestWordLevelTimestamps, "true");
                speechConfig.SetProperty(PropertyId.SpeechServiceResponse_RequestSnr, "true");

                // Enable streaming for lower latency
                speechConfig.SetProperty("SpeechServiceResponse_LanguageIdMode", "Continuous");
                speechConfig.SetProperty("SpeechServiceConnection_RecoMode", "INTERACTIVE");
                speechConfig.SetProperty("SpeechServiceConnection_EnableAudioLogging", "false");

                // Optimize for dictation scenarios
                speechConfig.SetProperty("SpeechServiceConnection_SingleLanguageIdPriority", "Latency");

                // Create audio config optimized for real-time with enhanced settings
                var audioConfig = AudioConfig.FromDefaultMicrophoneInput();

                // Set audio processing options for better quality
                audioConfig.SetProperty(PropertyId.Speech_LogFilename, "");  // Disable logging for performance

                // Create recognizer with optimized settings
                speechRecognizer = new SpeechRecognizer(speechConfig, audioConfig);

                // Setup event handlers
                speechRecognizer.Recognized += SpeechRecognizer_Recognized;
                speechRecognizer.Recognizing += SpeechRecognizer_Recognizing;
                speechRecognizer.Canceled += SpeechRecognizer_Canceled;

                // Test the microphone with a small delay to ensure initialization
                await Task.Delay(100);

                IsConfigured = true;
            }
            catch (Exception)
            {
                IsConfigured = false;
                throw;
            }
        }

        public void AddPhraseToGrammar(string phrase)
        {
            if (IsConfigured && speechRecognizer != null)
            {
                try
                {
                    var phraseList = PhraseListGrammar.FromRecognizer(speechRecognizer);
                    phraseList.AddPhrase(phrase);
                }
                catch (Exception ex)
                {
                    // Log phrase addition failure but don't throw
                    System.Diagnostics.Debug.WriteLine($"Failed to add phrase to grammar: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Gets the health status of the speech recognition service
        /// </summary>
        public bool IsHealthy()
        {
            if (!IsConfigured || speechRecognizer == null)
                return false;

            // Check if we've had too many consecutive errors
            if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS)
                return false;

            // Check if we've had recent successful recognition (within last 30 seconds during active listening)
            if (isListening && lastRecognitionTime != DateTime.MinValue)
            {
                var timeSinceLastRecognition = DateTime.Now - lastRecognitionTime;
                if (timeSinceLastRecognition.TotalSeconds > 30)
                {
                    return false; // No recognition for too long during active listening
                }
            }

            return true;
        }

        public async Task StartListeningAsync()
        {
            if (!isListening && IsConfigured && speechRecognizer != null)
            {
                try
                {
                    isListening = true;

                    // Start continuous recognition with timeout protection
                    var startTask = speechRecognizer.StartContinuousRecognitionAsync();
                    var timeoutTask = Task.Delay(5000); // 5 second timeout

                    var completedTask = await Task.WhenAny(startTask, timeoutTask);

                    if (completedTask == timeoutTask)
                    {
                        throw new TimeoutException("Speech recognition startup timed out");
                    }

                    await startTask; // Ensure any exceptions are propagated
                    continuousRecognition = true;
                }
                catch
                {
                    isListening = false;
                    throw;
                }
            }
        }

        public async Task StopListeningAsync()
        {
            if (isListening && IsConfigured && speechRecognizer != null && continuousRecognition)
            {
                // Set isListening false after stopping recognition to ensure final results are captured
                await speechRecognizer.StopContinuousRecognitionAsync();
                continuousRecognition = false;
                isListening = false;
            }
        }

        private void SpeechRecognizer_Recognized(object? sender, SpeechRecognitionEventArgs e)
        {
            // Always process recognition results, even if not listening
            if (!string.IsNullOrEmpty(e.Result.Text))
            {
                lastRecognitionTime = DateTime.Now;
                consecutiveErrors = 0; // Reset error count on successful recognition
                TextRecognized?.Invoke(this, e.Result.Text);
            }
        }

        private void SpeechRecognizer_Recognizing(object? sender, SpeechRecognitionEventArgs e)
        {
            // Provide real-time feedback even if final recognition is pending
            if (!string.IsNullOrEmpty(e.Result.Text))
            {
                RecognitionInProgress?.Invoke(this, e.Result.Text);
            }
        }

        private void SpeechRecognizer_Canceled(object? sender, SpeechRecognitionCanceledEventArgs e)
        {
            if (isListening)
            {
                consecutiveErrors++;
                string cancelReason = $"Recognition canceled: {e.Reason}";

                if (e.Reason == CancellationReason.Error)
                {
                    cancelReason += $" - Error details: {e.ErrorDetails}";

                    // If too many consecutive errors, suggest service restart
                    if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS)
                    {
                        cancelReason += $" - Consider restarting speech service (consecutive errors: {consecutiveErrors})";
                    }
                }

                RecognitionCanceled?.Invoke(this, cancelReason);
            }
        }

        public void Dispose()
        {
            if (speechRecognizer != null)
            {
                if (continuousRecognition)
                {
                    try
                    {
                        speechRecognizer.StopContinuousRecognitionAsync().GetAwaiter().GetResult();
                    }
                    catch { /* Ignore errors during cleanup */ }
                }

                speechRecognizer.Dispose();
                speechRecognizer = null;
            }
        }
    }
}
