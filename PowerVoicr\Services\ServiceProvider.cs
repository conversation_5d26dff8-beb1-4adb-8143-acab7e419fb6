using PowerVoicr.Services.Interfaces;
using System;
using System.Collections.Generic;

namespace PowerVoicr.Services
{
    public class ServiceProvider
    {
        private static ServiceProvider _instance;

        private readonly Dictionary<Type, object> _services = new Dictionary<Type, object>();

        public static ServiceProvider Instance => _instance ??= new ServiceProvider();

        private ServiceProvider()
        {
            // Register default service implementations
            RegisterService<IInputSimulationService>(new InputSimulationService());
            RegisterService<IPersonalDictionaryService>(new PersonalDictionaryService());
            RegisterService<ISpeechRecognitionService>(new AzureSpeechRecognitionService(GetService<IPersonalDictionaryService>()));
            RegisterService<IHotkeyService>(new HotkeyService());
            RegisterService<ICommandService>(new CommandService(GetService<IInputSimulationService>()));
        }

        public void RegisterService<T>(object implementation)
        {
            _services[typeof(T)] = implementation;
        }

        public T GetService<T>()
        {
            if (_services.TryGetValue(typeof(T), out object service))
            {
                return (T)service;
            }

            throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
        }
    }
}