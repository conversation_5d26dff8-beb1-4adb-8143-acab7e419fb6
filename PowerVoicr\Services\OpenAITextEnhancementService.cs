using PowerVoicr.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace PowerVoicr.Services
{
    public class OpenAITextEnhancementService : ITextEnhancementService
    {
        private readonly HttpClient httpClient;
        private string apiKey = "";
        private const string OPENAI_API_URL = "https://api.openai.com/v1/chat/completions";
        private const string DEFAULT_MODEL = "gpt-3.5-turbo";
        
        public event EventHandler<TextEnhancementCompletedEventArgs>? EnhancementCompleted;
        public event EventHandler<TextEnhancementErrorEventArgs>? EnhancementError;
        
        public bool IsConfigured { get; private set; }
        
        public OpenAITextEnhancementService()
        {
            httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);
        }
        
        public async Task InitializeAsync()
        {
            try
            {
                Debug.WriteLine("[OPENAI] Initializing OpenAI Text Enhancement Service...");
                Console.WriteLine("[OPENAI] Initializing OpenAI Text Enhancement Service...");
                
                // Get API key from settings
                apiKey = Settings.Instance.OpenAIApiKey;
                
                if (string.IsNullOrEmpty(apiKey))
                {
                    Debug.WriteLine("[OPENAI] WARNING: OpenAI API key not configured");
                    Console.WriteLine("[OPENAI] WARNING: OpenAI API key not configured");
                    IsConfigured = false;
                    return;
                }
                
                // Set up HTTP client headers
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
                httpClient.DefaultRequestHeaders.Add("User-Agent", "PowerVoicr/1.0");
                
                // Test the connection with a simple request
                await TestConnectionAsync();
                
                IsConfigured = true;
                Debug.WriteLine("[OPENAI] OpenAI Text Enhancement Service initialized successfully!");
                Console.WriteLine("[OPENAI] OpenAI Text Enhancement Service initialized successfully!");
            }
            catch (Exception ex)
            {
                IsConfigured = false;
                Debug.WriteLine($"[OPENAI] ERROR: Failed to initialize OpenAI service: {ex.Message}");
                Console.WriteLine($"[OPENAI] ERROR: Failed to initialize OpenAI service: {ex.Message}");
                throw;
            }
        }
        
        public async Task<string> EnhanceTextAsync(string text, TextEnhancementOptions? options = null)
        {
            if (!IsConfigured)
                throw new InvalidOperationException("OpenAI service is not configured");
                
            if (string.IsNullOrWhiteSpace(text))
                return text;
                
            options ??= new TextEnhancementOptions();
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                string prompt = BuildEnhancementPrompt(text, options);
                string enhancedText = await CallOpenAIAsync(prompt);
                
                stopwatch.Stop();
                
                EnhancementCompleted?.Invoke(this, new TextEnhancementCompletedEventArgs
                {
                    OriginalText = text,
                    EnhancedText = enhancedText,
                    Options = options,
                    ProcessingTime = stopwatch.Elapsed
                });
                
                return enhancedText;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                EnhancementError?.Invoke(this, new TextEnhancementErrorEventArgs
                {
                    OriginalText = text,
                    Error = ex,
                    ErrorMessage = ex.Message,
                    IsRetryable = IsRetryableError(ex)
                });
                
                throw;
            }
        }
        
        public async Task<string> CorrectGrammarAsync(string text)
        {
            var options = new TextEnhancementOptions
            {
                CorrectGrammar = true,
                FixSpelling = true,
                ImproveClarity = false,
                Style = TextStyle.Professional
            };
            
            return await EnhanceTextAsync(text, options);
        }
        
        public async Task<string> FormatTextAsync(string text, TextStyle style = TextStyle.Professional)
        {
            var options = new TextEnhancementOptions
            {
                Style = style,
                CorrectGrammar = true,
                FixSpelling = true,
                ImproveClarity = true
            };
            
            return await EnhanceTextAsync(text, options);
        }
        
        public async Task<string> ExpandAndClarifyAsync(string text)
        {
            var options = new TextEnhancementOptions
            {
                ExpandAbbreviations = true,
                ImproveClarity = true,
                Style = TextStyle.Professional
            };
            
            return await EnhanceTextAsync(text, options);
        }
        
        public async Task<string> SummarizeAsync(string text, int maxLength = 200)
        {
            string prompt = $"Summarize the following text in {maxLength} characters or less, maintaining the key points:\n\n{text}";
            return await CallOpenAIAsync(prompt);
        }
        
        public async Task<string> TranslateAsync(string text, string targetLanguage)
        {
            string prompt = $"Translate the following text to {targetLanguage}:\n\n{text}";
            return await CallOpenAIAsync(prompt);
        }
        
        public async Task<List<TextSuggestion>> GetSuggestionsAsync(string text)
        {
            // For now, return empty list - this would require more complex parsing
            // In a full implementation, this would analyze the text and return specific suggestions
            return new List<TextSuggestion>();
        }
        
        public async Task<string> QuickEnhanceAsync(string text)
        {
            if (!IsConfigured || string.IsNullOrWhiteSpace(text))
                return text;
                
            try
            {
                string prompt = $"Quickly fix grammar and spelling in this text, keep it concise:\n\n{text}";
                return await CallOpenAIAsync(prompt, maxTokens: 150);
            }
            catch
            {
                // Return original text if quick enhancement fails
                return text;
            }
        }
        
        private async Task<string> CallOpenAIAsync(string prompt, int maxTokens = 500)
        {
            var requestBody = new
            {
                model = DEFAULT_MODEL,
                messages = new[]
                {
                    new { role = "system", content = "You are a helpful assistant that improves text quality. Return only the improved text without explanations." },
                    new { role = "user", content = prompt }
                },
                max_tokens = maxTokens,
                temperature = 0.3,
                top_p = 1.0,
                frequency_penalty = 0.0,
                presence_penalty = 0.0
            };
            
            string jsonContent = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            
            var response = await httpClient.PostAsync(OPENAI_API_URL, content);
            response.EnsureSuccessStatusCode();
            
            string responseContent = await response.Content.ReadAsStringAsync();
            var responseJson = JsonSerializer.Deserialize<JsonElement>(responseContent);
            
            if (responseJson.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
            {
                var firstChoice = choices[0];
                if (firstChoice.TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var messageContent))
                {
                    return messageContent.GetString()?.Trim() ?? "";
                }
            }
            
            throw new Exception("Invalid response from OpenAI API");
        }
        
        private async Task TestConnectionAsync()
        {
            try
            {
                await CallOpenAIAsync("Test", maxTokens: 10);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to connect to OpenAI API: {ex.Message}", ex);
            }
        }
        
        private static string BuildEnhancementPrompt(string text, TextEnhancementOptions options)
        {
            var promptBuilder = new StringBuilder();
            promptBuilder.AppendLine("Please improve the following text according to these requirements:");
            
            if (options.CorrectGrammar)
                promptBuilder.AppendLine("- Fix grammar errors");
                
            if (options.FixSpelling)
                promptBuilder.AppendLine("- Correct spelling mistakes");
                
            if (options.ImproveClarity)
                promptBuilder.AppendLine("- Improve clarity and readability");
                
            if (options.ExpandAbbreviations)
                promptBuilder.AppendLine("- Expand abbreviations where appropriate");
                
            if (options.AddPunctuation)
                promptBuilder.AppendLine("- Add proper punctuation");
                
            promptBuilder.AppendLine($"- Use {options.Style.ToString().ToLower()} style");
            
            if (options.MaxLength > 0)
                promptBuilder.AppendLine($"- Keep under {options.MaxLength} characters");
                
            if (!string.IsNullOrEmpty(options.Context))
                promptBuilder.AppendLine($"- Context: {options.Context}");
                
            promptBuilder.AppendLine("\nText to improve:");
            promptBuilder.AppendLine(text);
            
            return promptBuilder.ToString();
        }
        
        private static bool IsRetryableError(Exception ex)
        {
            // Check if the error is retryable (network issues, rate limits, etc.)
            return ex is HttpRequestException || 
                   ex.Message.Contains("rate limit") || 
                   ex.Message.Contains("timeout");
        }
        
        public void Dispose()
        {
            httpClient?.Dispose();
        }
    }
}
