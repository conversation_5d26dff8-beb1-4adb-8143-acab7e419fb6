using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using PowerVoicr.Services;

namespace PowerVoicr.Diagnostics
{
    public partial class RightAltTestForm : Form
    {
        private ListBox logListBox;
        private Label instructionLabel;
        private KeyboardHook testHook;

        public RightAltTestForm()
        {
            InitializeComponent();
            SetupKeyboardHook();
        }

        private void InitializeComponent()
        {
            this.Text = "Right Alt Key Detection Test";
            this.Size = new System.Drawing.Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;

            instructionLabel = new Label
            {
                Text = "Test both Left Alt and Right Alt keys. Events will be logged below:",
                Location = new System.Drawing.Point(10, 10),
                Size = new System.Drawing.Size(580, 40),
                Font = new System.Drawing.Font("Arial", 10)
            };

            logListBox = new ListBox
            {
                Location = new System.Drawing.Point(10, 50),
                Size = new System.Drawing.Size(570, 300),
                Font = new System.Drawing.Font("Consolas", 9)
            };

            this.Controls.Add(instructionLabel);
            this.Controls.Add(logListBox);
        }

        private void SetupKeyboardHook()
        {
            testHook = new KeyboardHook();
            testHook.KeyDown += TestHook_KeyDown;
            testHook.KeyUp += TestHook_KeyUp;
            testHook.Install();

            LogMessage("Keyboard hook installed. Press Alt keys to test...");
        }

        private void TestHook_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.RMenu || e.KeyCode == Keys.LMenu || e.KeyCode == Keys.Menu)
            {
                string keyName = e.KeyCode == Keys.RMenu ? "RIGHT ALT" :
                                e.KeyCode == Keys.LMenu ? "LEFT ALT" : "GENERIC ALT";

                LogMessage($"🔽 {keyName} KEY DOWN - Code: {e.KeyCode}, Modifiers: {e.Modifiers}");

                // Also check Win32 API state
                short rightAltState = GetAsyncKeyState(0xA5); // VK_RMENU
                short leftAltState = GetAsyncKeyState(0xA4);  // VK_LMENU

                LogMessage($"   📊 API States - Right Alt: 0x{rightAltState:X4}, Left Alt: 0x{leftAltState:X4}");
            }
        }

        private void TestHook_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.RMenu || e.KeyCode == Keys.LMenu || e.KeyCode == Keys.Menu)
            {
                string keyName = e.KeyCode == Keys.RMenu ? "RIGHT ALT" :
                                e.KeyCode == Keys.LMenu ? "LEFT ALT" : "GENERIC ALT";

                LogMessage($"🔼 {keyName} KEY UP - Code: {e.KeyCode}");
            }
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            string logEntry = $"[{timestamp}] {message}";

            if (logListBox.InvokeRequired)
            {
                logListBox.Invoke(new Action(() =>
                {
                    logListBox.Items.Add(logEntry);
                    logListBox.TopIndex = logListBox.Items.Count - 1;
                }));
            }
            else
            {
                logListBox.Items.Add(logEntry);
                logListBox.TopIndex = logListBox.Items.Count - 1;
            }

            Debug.WriteLine(logEntry);
        }

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            testHook?.Uninstall();
            testHook?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
