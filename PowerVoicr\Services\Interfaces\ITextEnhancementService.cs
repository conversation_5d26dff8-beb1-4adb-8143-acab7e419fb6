using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PowerVoicr.Services.Interfaces
{
    public interface ITextEnhancementService : IDisposable
    {
        /// <summary>
        /// Event fired when text enhancement is completed
        /// </summary>
        event EventHandler<TextEnhancementCompletedEventArgs> EnhancementCompleted;
        
        /// <summary>
        /// Event fired when text enhancement fails
        /// </summary>
        event EventHandler<TextEnhancementErrorEventArgs> EnhancementError;
        
        /// <summary>
        /// Gets whether the service is configured and ready to use
        /// </summary>
        bool IsConfigured { get; }
        
        /// <summary>
        /// Initializes the text enhancement service
        /// </summary>
        Task InitializeAsync();
        
        /// <summary>
        /// Enhances text with grammar correction, formatting, and style improvements
        /// </summary>
        Task<string> EnhanceTextAsync(string text, TextEnhancementOptions? options = null);
        
        /// <summary>
        /// Corrects grammar and spelling in the provided text
        /// </summary>
        Task<string> CorrectGrammarAsync(string text);
        
        /// <summary>
        /// Formats text according to specified style (email, document, casual, etc.)
        /// </summary>
        Task<string> FormatTextAsync(string text, TextStyle style = TextStyle.Professional);
        
        /// <summary>
        /// Expands abbreviations and improves clarity
        /// </summary>
        Task<string> ExpandAndClarifyAsync(string text);
        
        /// <summary>
        /// Summarizes long text into key points
        /// </summary>
        Task<string> SummarizeAsync(string text, int maxLength = 200);
        
        /// <summary>
        /// Translates text to specified language
        /// </summary>
        Task<string> TranslateAsync(string text, string targetLanguage);
        
        /// <summary>
        /// Gets suggestions for improving the text
        /// </summary>
        Task<List<TextSuggestion>> GetSuggestionsAsync(string text);
        
        /// <summary>
        /// Processes text in real-time with minimal latency
        /// </summary>
        Task<string> QuickEnhanceAsync(string text);
    }
    
    public class TextEnhancementOptions
    {
        public TextStyle Style { get; set; } = TextStyle.Professional;
        public bool CorrectGrammar { get; set; } = true;
        public bool FixSpelling { get; set; } = true;
        public bool ImproveClarity { get; set; } = true;
        public bool ExpandAbbreviations { get; set; } = false;
        public bool AddPunctuation { get; set; } = true;
        public bool PreserveFormatting { get; set; } = false;
        public int MaxLength { get; set; } = 0; // 0 = no limit
        public string? TargetAudience { get; set; }
        public string? Context { get; set; }
    }
    
    public enum TextStyle
    {
        Casual,
        Professional,
        Academic,
        Technical,
        Creative,
        Email,
        Document,
        Presentation,
        SocialMedia,
        Custom
    }
    
    public class TextSuggestion
    {
        public string OriginalText { get; set; } = "";
        public string SuggestedText { get; set; } = "";
        public string Reason { get; set; } = "";
        public SuggestionType Type { get; set; } = SuggestionType.Grammar;
        public double Confidence { get; set; } = 0.0;
        public int StartIndex { get; set; } = 0;
        public int Length { get; set; } = 0;
    }
    
    public enum SuggestionType
    {
        Grammar,
        Spelling,
        Style,
        Clarity,
        Punctuation,
        Formatting,
        Vocabulary,
        Tone
    }
    
    public class TextEnhancementCompletedEventArgs : EventArgs
    {
        public string OriginalText { get; set; } = "";
        public string EnhancedText { get; set; } = "";
        public TextEnhancementOptions Options { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
        public List<TextSuggestion> AppliedSuggestions { get; set; } = new();
    }
    
    public class TextEnhancementErrorEventArgs : EventArgs
    {
        public string OriginalText { get; set; } = "";
        public Exception Error { get; set; } = new Exception();
        public string ErrorMessage { get; set; } = "";
        public bool IsRetryable { get; set; } = false;
    }
}
