using PowerVoicr.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PowerVoicr.Services
{
    public class PersonalDictionaryService : IPersonalDictionaryService
    {
        private readonly Dictionary<string, PersonalDictionaryEntry> dictionary;
        private readonly Dictionary<string, List<string>> corrections;
        private readonly string dictionaryFilePath;
        private readonly string correctionsFilePath;
        
        public event EventHandler<string>? WordLearned;
        public event EventHandler<WordCorrectionEventArgs>? WordCorrected;
        
        public PersonalDictionaryService()
        {
            dictionary = new Dictionary<string, PersonalDictionaryEntry>(StringComparer.OrdinalIgnoreCase);
            corrections = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            
            // Set up file paths
            string appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "PowerVoicr");
            Directory.CreateDirectory(appDataPath);
            
            dictionaryFilePath = Path.Combine(appDataPath, "personal_dictionary.json");
            correctionsFilePath = Path.Combine(appDataPath, "word_corrections.json");
            
            LoadDictionary();
            LoadCorrections();
        }
        
        public IReadOnlyList<PersonalDictionaryEntry> GetAllWords()
        {
            return dictionary.Values.ToList().AsReadOnly();
        }
        
        public void AddWord(string word, string pronunciation = "", WordCategory category = WordCategory.General)
        {
            if (string.IsNullOrWhiteSpace(word))
                return;
                
            word = word.Trim().ToLowerInvariant();
            
            if (dictionary.ContainsKey(word))
            {
                // Update existing word
                var entry = dictionary[word];
                entry.UsageCount++;
                entry.LastUsed = DateTime.Now;
                if (!string.IsNullOrEmpty(pronunciation))
                    entry.Pronunciation = pronunciation;
                if (category != WordCategory.General)
                    entry.Category = category;
            }
            else
            {
                // Add new word
                dictionary[word] = new PersonalDictionaryEntry
                {
                    Word = word,
                    Pronunciation = pronunciation,
                    Category = category,
                    UsageCount = 1,
                    DateAdded = DateTime.Now,
                    LastUsed = DateTime.Now,
                    Confidence = 1.0
                };
                
                WordLearned?.Invoke(this, word);
            }
            
            SaveDictionary();
        }
        
        public bool RemoveWord(string word)
        {
            if (string.IsNullOrWhiteSpace(word))
                return false;
                
            word = word.Trim().ToLowerInvariant();
            bool removed = dictionary.Remove(word);
            
            if (removed)
            {
                SaveDictionary();
            }
            
            return removed;
        }
        
        public bool ContainsWord(string word)
        {
            if (string.IsNullOrWhiteSpace(word))
                return false;
                
            return dictionary.ContainsKey(word.Trim().ToLowerInvariant());
        }
        
        public IList<string> GetSuggestions(string word, int maxSuggestions = 5)
        {
            if (string.IsNullOrWhiteSpace(word))
                return new List<string>();
                
            word = word.Trim().ToLowerInvariant();
            var suggestions = new List<(string Word, double Score)>();
            
            foreach (var entry in dictionary.Values)
            {
                double score = CalculateSimilarity(word, entry.Word);
                if (score > 0.3) // Minimum similarity threshold
                {
                    suggestions.Add((entry.Word, score));
                }
            }
            
            return suggestions
                .OrderByDescending(s => s.Score)
                .Take(maxSuggestions)
                .Select(s => s.Word)
                .ToList();
        }
        
        public void RecordCorrection(string originalWord, string correctedWord)
        {
            if (string.IsNullOrWhiteSpace(originalWord) || string.IsNullOrWhiteSpace(correctedWord))
                return;
                
            originalWord = originalWord.Trim().ToLowerInvariant();
            correctedWord = correctedWord.Trim().ToLowerInvariant();
            
            if (!corrections.ContainsKey(originalWord))
            {
                corrections[originalWord] = new List<string>();
            }
            
            corrections[originalWord].Add(correctedWord);
            
            // Keep only the last 10 corrections for each word
            if (corrections[originalWord].Count > 10)
            {
                corrections[originalWord].RemoveAt(0);
            }
            
            // Add corrected word to dictionary
            AddWord(correctedWord);
            
            WordCorrected?.Invoke(this, new WordCorrectionEventArgs
            {
                OriginalWord = originalWord,
                CorrectedWord = correctedWord,
                Timestamp = DateTime.Now
            });
            
            SaveCorrections();
        }
        
        public IList<string> GetCommonCorrections(string word)
        {
            if (string.IsNullOrWhiteSpace(word))
                return new List<string>();
                
            word = word.Trim().ToLowerInvariant();
            
            if (!corrections.ContainsKey(word))
                return new List<string>();
                
            return corrections[word]
                .GroupBy(c => c)
                .OrderByDescending(g => g.Count())
                .Select(g => g.Key)
                .Take(5)
                .ToList();
        }
        
        public void LearnFromText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return;
                
            // Extract words using regex
            var words = Regex.Matches(text.ToLowerInvariant(), @"\b[a-zA-Z]+\b")
                .Cast<Match>()
                .Select(m => m.Value)
                .Where(w => w.Length > 2) // Only words with 3+ characters
                .Distinct();
                
            foreach (string word in words)
            {
                // Categorize words automatically
                WordCategory category = CategorizeWord(word);
                AddWord(word, "", category);
            }
        }
        
        public void ExportDictionary(string filePath)
        {
            try
            {
                var exportData = new
                {
                    Dictionary = dictionary.Values.ToList(),
                    Corrections = corrections,
                    ExportDate = DateTime.Now,
                    Version = "1.0"
                };
                
                string json = JsonSerializer.Serialize(exportData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to export dictionary: {ex.Message}");
            }
        }
        
        public void ImportDictionary(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return;
                    
                string json = File.ReadAllText(filePath);
                var importData = JsonSerializer.Deserialize<JsonElement>(json);
                
                if (importData.TryGetProperty("Dictionary", out var dictionaryElement))
                {
                    var entries = JsonSerializer.Deserialize<List<PersonalDictionaryEntry>>(dictionaryElement.GetRawText());
                    if (entries != null)
                    {
                        foreach (var entry in entries)
                        {
                            dictionary[entry.Word] = entry;
                        }
                    }
                }
                
                if (importData.TryGetProperty("Corrections", out var correctionsElement))
                {
                    var importedCorrections = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(correctionsElement.GetRawText());
                    if (importedCorrections != null)
                    {
                        foreach (var kvp in importedCorrections)
                        {
                            corrections[kvp.Key] = kvp.Value;
                        }
                    }
                }
                
                SaveDictionary();
                SaveCorrections();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to import dictionary: {ex.Message}");
            }
        }
        
        public PersonalDictionaryStats GetStatistics()
        {
            var stats = new PersonalDictionaryStats
            {
                TotalWords = dictionary.Count,
                TotalUsages = dictionary.Values.Sum(e => e.UsageCount),
                LastUpdated = DateTime.Now
            };
            
            // Words by category
            stats.WordsByCategory = dictionary.Values
                .GroupBy(e => e.Category)
                .ToDictionary(g => g.Key, g => g.Count());
                
            // Most used words
            stats.MostUsedWords = dictionary.Values
                .OrderByDescending(e => e.UsageCount)
                .Take(10)
                .ToList();
                
            // Recently added words
            stats.RecentlyAddedWords = dictionary.Values
                .OrderByDescending(e => e.DateAdded)
                .Take(10)
                .ToList();
                
            return stats;
        }
        
        private void LoadDictionary()
        {
            try
            {
                if (File.Exists(dictionaryFilePath))
                {
                    string json = File.ReadAllText(dictionaryFilePath);
                    var entries = JsonSerializer.Deserialize<List<PersonalDictionaryEntry>>(json);
                    
                    if (entries != null)
                    {
                        foreach (var entry in entries)
                        {
                            dictionary[entry.Word] = entry;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load dictionary: {ex.Message}");
            }
        }
        
        private void SaveDictionary()
        {
            try
            {
                string json = JsonSerializer.Serialize(dictionary.Values.ToList(), new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                File.WriteAllText(dictionaryFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save dictionary: {ex.Message}");
            }
        }
        
        private void LoadCorrections()
        {
            try
            {
                if (File.Exists(correctionsFilePath))
                {
                    string json = File.ReadAllText(correctionsFilePath);
                    var loadedCorrections = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(json);
                    
                    if (loadedCorrections != null)
                    {
                        foreach (var kvp in loadedCorrections)
                        {
                            corrections[kvp.Key] = kvp.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load corrections: {ex.Message}");
            }
        }
        
        private void SaveCorrections()
        {
            try
            {
                string json = JsonSerializer.Serialize(corrections, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                File.WriteAllText(correctionsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save corrections: {ex.Message}");
            }
        }
        
        private static double CalculateSimilarity(string word1, string word2)
        {
            if (string.IsNullOrEmpty(word1) || string.IsNullOrEmpty(word2))
                return 0;
                
            // Simple Levenshtein distance-based similarity
            int distance = LevenshteinDistance(word1, word2);
            int maxLength = Math.Max(word1.Length, word2.Length);
            
            return 1.0 - (double)distance / maxLength;
        }
        
        private static int LevenshteinDistance(string s1, string s2)
        {
            int[,] matrix = new int[s1.Length + 1, s2.Length + 1];
            
            for (int i = 0; i <= s1.Length; i++)
                matrix[i, 0] = i;
                
            for (int j = 0; j <= s2.Length; j++)
                matrix[0, j] = j;
                
            for (int i = 1; i <= s1.Length; i++)
            {
                for (int j = 1; j <= s2.Length; j++)
                {
                    int cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }
            
            return matrix[s1.Length, s2.Length];
        }
        
        private static WordCategory CategorizeWord(string word)
        {
            // Simple heuristics for word categorization
            if (word.Length <= 3 && word.ToUpperInvariant() == word)
                return WordCategory.Abbreviation;
                
            if (char.IsUpper(word[0]) && word.Length > 3)
                return WordCategory.Name;
                
            // Add more sophisticated categorization logic here
            return WordCategory.General;
        }
        
        public void Dispose()
        {
            SaveDictionary();
            SaveCorrections();
        }
    }
}
